//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：SetParaBao.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.11.20
//---------------------------------------------------------
#ifndef __SETPARABAO_H__
#define __SETPARABAO_H__

#include <stdint.h>
#include "fpgad.h"

#define SETPARA_DATAOUT_FPGA_FREQ               200//FPGA的硬件中断频率，用于输出频率设置的基准参数

#define SETPARA_RXBUFFER_DMA_SIZE		256	
#define SETPARA_TXBUFFER_DMA_SIZE		64

//包头定义
#define SETPARA_HEADER_0						(0xFA)							//通讯包数据-包头-0字节
#define SETPARA_HEADER_1						(0x55)							//通讯包数据-包头-1字节
#define SETPARA_HEADER_2						(0xAF)							//通讯包数据-包头-2字节

//包尾定义
#define SETPARA_END_0								(0x00)							//通讯包数据-包尾-0字节
#define SETPARA_END_1								(0xFF)							//通讯包数据-包尾-1字节



//协议报文类型
//上位机发送，下位机导航口接收
#define SETPARA_TYPE0_output				(0x11AA)	//输出参数
#define SETPARA_TYPE0_baud				  (0x13AA)	//波特率
#define SETPARA_TYPE0_frequency			(0x14AA)	//数据输出频率
#define SETPARA_TYPE0_gnss				  (0x18AA)	//设置GNSS杆臂参数
#define SETPARA_TYPE0_angle				  (0x19AA)	//设置天线安装角度
#define SETPARA_TYPE0_vector				(0x1AAA)	//惯导-后轮轴中心位置矢量
#define SETPARA_TYPE0_deviation			(0x1BAA)	//惯导角度安装偏差
#define SETPARA_TYPE0_initvalue     (0x21AA)	//GNSS初始值
#define SETPARA_TYPE0_coord				  (0x1EAA)	//设置用户坐标轴
#define SETPARA_TYPE0_offsetime			(0x25AA)	//设置静态测零偏时间
#define SETPARA_TYPE0_transfer			(0xCCAA)	//设置透传协议
#define SETPARA_TYPE0_solidify			(0xF1AA)	//固化参数
#define SETPARA_TYPE0_factory				(0xF2AA)	//恢复出厂设置
#define SETPARA_TYPE0_setpara				(0xF3AA)	//设置所有参数
#define SETPARA_TYPE0_readpara			(0xF4AA)	//参数回读
#define SETPARA_TYPE0_readver				(0xF5AA)	//读取版本号
#define SETPARA_TYPE0_gps           (0xC3AA)	//GPS中断类型
#define SETPARA_TYPE0_type				  (0xC4AA)	//数据输出Type
#define SETPARA_TYPE0_debug				  (0xC1AA)	//是否开启Debug模式
#define SETPARA_TYPE0_gyro				  (0xC2AA)	//陀螺类型
#define SETPARA_TYPE0_calibration   (0x30AA)	//标定参数
#define SETPARA_TYPE0_temoffset     (0x34AA)	//温度补偿
#define SETPARA_TYPE0_KalmanQ				(0x31AA)	//卡尔曼滤波Q矩阵
#define SETPARA_TYPE0_KalmanR				(0x32AA)	//卡尔曼滤波R矩阵
#define SETPARA_TYPE0_filter				(0x33AA)	//间接滤波校正系数
#define SETPARA_TYPE0_FactorGyro    (0x35AA)	//陀螺标定因数
#define SETPARA_TYPE0_FactorAcc     (0x36AA)	//加计标定因数
#define SETPARA_TYPE0_UPDATE_START	(0x51AA)	//软件升级开始命令
#define SETPARA_TYPE0_UPDATE_SEND		(0x52AA)	//发送升级包命令
#define SETPARA_TYPE0_UPDATE_END		(0x55AA)	//升级包完成命令
#define SETPARA_TYPE0_UPDATE_STOP		(0x59AA)	//升级终止命令



//下位机导航口发送，上位机接收
#define SETPARA_TYPE1_output			  (0x11AA)	//输出参数
#define SETPARA_TYPE1_baud				  (0x13AA)	//波特率
#define SETPARA_TYPE1_frequency		  (0x14AA)	//数据输出频率
#define SETPARA_TYPE1_gnss				  (0x18AA)	//设置GNSS杆臂参数
#define SETPARA_TYPE1_angle				  (0x19AA)	//设置天线安装角度
#define SETPARA_TYPE1_vector			  (0x1AAA)	//惯导-后轮轴中心位置矢量
#define SETPARA_TYPE1_deviation		  (0x1BAA)	//惯导角度安装偏差
#define SETPARA_TYPE1_initvalue     (0x21AA)	//GNSS初始值
#define SETPARA_TYPE1_coord				  (0x1EAA)	//设置用户坐标轴
#define SETPARA_TYPE1_offsetime		  (0x25AA)	//设置静态测零偏时间
#define SETPARA_TYPE1_transfer		  (0xCCAA)	//设置透传协议
#define SETPARA_TYPE1_solidify		  (0xF1AA)	//固化参数
#define SETPARA_TYPE1_factory			  (0xF2AA)	//恢复出厂设置
#define SETPARA_TYPE1_setpara			  (0xF3AA)	//设置所有参数
#define SETPARA_TYPE1_readpara		  (0xF4AA)	//参数回读
#define SETPARA_TYPE1_readver			  (0xF5AA)	//读取版本号
#define SETPARA_TYPE1_gps           (0xC3AA)	//GPS中断类型
#define SETPARA_TYPE1_type				  (0xC4AA)	//数据输出Type
#define SETPARA_TYPE1_debug				  (0xC1AA)	//是否开启Debug模式
#define SETPARA_TYPE1_gyro				  (0xC2AA)	//陀螺类型
#define SETPARA_TYPE1_calibration   (0x30AA)	//标定参数
#define SETPARA_TYPE1_temoffset     (0x34AA)	//温度补偿
#define SETPARA_TYPE1_KalmanQ			  (0x31AA)	//卡尔曼滤波Q矩阵
#define SETPARA_TYPE1_KalmanR			  (0x32AA)	//卡尔曼滤波R矩阵
#define SETPARA_TYPE1_filter			  (0x33AA)	//间接滤波校正系数
#define SETPARA_TYPE1_FactorGyro    (0x35AA)	//陀螺标定因数
#define SETPARA_TYPE1_FactorAcc     (0x36AA)	//加计标定因数
#define SETPARA_TYPE1_UPDATE_START	(0x51AA)	//软件升级开始命令
#define SETPARA_TYPE1_UPDATE_SEND		(0x52AA)	//发送升级包命令
#define SETPARA_TYPE1_UPDATE_END		(0x55AA)	//升级包完成命令
#define SETPARA_TYPE1_UPDATE_STOP		(0x59AA)	//升级终止命令

//-------------软件版本信息--------------------
#define VERMAIN			(0x01)	//主版本号
#define VERMINOR 		(0x01)	//次版本号
#define REVISION 		(0x01)  //修订号
#define VERYEAR 		(2025)  //年份	
#define VERMONTH    (3)	  //月份
#define VERDAY      (5)    //日份
#define PLAN        (0)     //方案（GD-0，先辑-1，芯驰-2，英飞凌-3，...）
#define SUFFIX      (1)     //后缀（内测版-0，公测版-1，候选发布版-2，正式版-3，...）
#define CUSTOM      (2)   //客户定制版（0~254），通用版：255	



#pragma pack(1)
//--包头--------------------
typedef struct _parabaghead																
{
	unsigned char header[3];//报文头固定:0xAF，0x55，0xFA																														
	unsigned short Type;	//报文类型
        unsigned short len;	//报文长度														
}parabaghead,*p_parabaghead;

//--包尾--------------------
typedef struct _parabagend																
{	
        unsigned char  check;	//校验码														
	unsigned char  ender[2];//报文尾固定:0x00 0xFF															
}parabagend,*p_parabagend;



//--------下位机接收信息结构体-----------------
//参数回读,读取版本号,固化参数,恢复出厂设置共用信息体结构
typedef struct _parabag_info_pararead													
{
	unsigned short 	ParaType[4];																
	unsigned char	reserve[238];//预留：置0x00
}parabag_info_pararead,*p_parabag_info_pararead;

//设置类型 是否开启Debug模式，陀螺类型，GPS中断类型，数据输出,软件升级开始,完成，终止命令共用信息体结构
typedef struct _parabag_info_SetType													
{
	unsigned char 	SetTypePara;																
	unsigned char	reserve[245];//预留：置0x00
}parabag_info_SetType,*p_parabag_info_SetType;

//设置用户坐标轴信息体结构
typedef struct _parabag_info_SetCoord
{
	unsigned char 	SetCoordPara;	//设置坐标
        unsigned char 	SetDirPara;	//设置方向													
	unsigned char	reserve[244];//预留：置0x00
}parabag_info_SetCoord,*p_parabag_info_SetCoord;

//波特率信息体结构
typedef struct _parabag_info_Setbaud
{
	unsigned short 	SetbaudPara;//设置波特率												
	unsigned char	reserve[244];//预留：置0x00
}parabag_info_Setbaud,*p_parabag_info_Setbaud;


//数据输出频率信息体结构
typedef struct _parabag_info_Setfrequency
{
	unsigned short 	SetfrePara;//输出频率												
	unsigned char	reserve[244];//预留：置0x00
}parabag_info_Setfrequency,*p_parabag_info_Setfrequency;

//设置静态测零偏时间信息体结构
typedef struct _parabag_info_SetTime
{
	unsigned short 	SetTimepara;//静态测零偏时间												
	unsigned char	reserve[244];//预留：置0x00
}parabag_info_SetTime,*p_parabag_info_SetTime;

//发送升级包命令信息体结构
typedef struct _parabag_info_UpdateSend
{
	unsigned short 	BaoIndex;	//当前包编号 0,1,2,3,........
	unsigned short 	TotalBao;	//总包数量
	unsigned char 	Length;		//当前有效数据长度
	unsigned char 	UpdateData[128];//升级数据
	
	unsigned char	reserve[111];	//预留：置0x00	
        unsigned short 	Count;          //帧计数						
}parabag_info_UpdateSend,*p_parabag_info_UpdateSend;

//设置GNSS杆臂参数信息体结构
typedef struct _parabag_info_Setgnss
{
	float 	armX;                   //杆臂X
	float 	armY;                   //杆臂Y
	float 	armZ;                   //杆臂Z
        unsigned char	reserve[234];	//预留：置0x00	
}parabag_info_Setgnss,*p_parabag_info_Setgnss;

//设置天线安装角度信息体结构
typedef struct _parabag_info_SetAngle
{
	float 	angleX;                   //X轴角度
	float 	angleY;                   //Y轴角度
	float 	angleZ;                   //Z轴角度
        unsigned char	reserve[234];	//预留：置0x00	
}parabag_info_SetAngle,*p_parabag_info_SetAngle;

//惯导角度安装偏差信息体结构
typedef struct _parabag_info_SetDeviation
{
        float 	pitch;                  //俯仰角
	float 	roll;                   //横滚角
        float 	Course;                 //航向角
        unsigned char	reserve[234];	//预留：置0x00	
}parabag_info_SetDeviation,*p_parabag_info_SetDeviation;

//惯导-后轮轴中心位置矢量信息体结构
typedef struct _parabag_info_SetVector
{
        float 	vectorX;                  //X轴位置矢量
	float 	vectorY;                  //Y轴位置矢量
        float 	vectorZ;                  //Z轴位置矢量
        unsigned char	reserve[234];     //预留：置0x00	
}parabag_info_SetVector,*p_parabag_info_SetVector;


//GNSS初始值信息体结构
typedef struct _parabag_info_SetGnssinitval
{
        float 	pitch;                  //俯仰角
	float 	roll;                   //横滚角
        float 	Course;                 //航向角
        float 	yaw;                    //偏航角

        float 	longitude;              //经度
	float 	latitude;               //纬度
	float 	hight;                  //高度

        unsigned char	reserve[218];	//预留：置0x00	
}parabag_info_SetGnssinitval,*p_parabag_info_SetGnssinitval;


//卡尔曼滤波R矩阵信息体结构
typedef struct _parabag_info_SetKalmanR
{
        float 	R_heading;                  
	float 	R_velocity[3];                  
        float 	R_position[3];   
        float 	R_extra[9];    
                    
        unsigned char	reserve[182];     //预留：置0x00	
}parabag_info_SetKalmanR,*p_parabag_info_SetKalmanR;

//卡尔曼滤波Q矩阵信息体结构
typedef struct _parabag_info_SetKalmanQ
{
        float 	Q_phi[3];                  
	float 	Q_velocity[3];                  
        float 	Q_position[3];   
        float 	Q_gyro_bias[3]; 
        float   Q_acc_bias[3];
        float   Q_extra[9];   
                    
        unsigned char	reserve[150];     //预留：置0x00	
}parabag_info_SetKalmanQ,*p_parabag_info_SetKalmanQ;


//间接滤波校正系数信息体结构
typedef struct _parabag_info_SetFilter
{
        float 	D_phi[3];                  
	float 	D_velocity[3];                  
        float 	D_position[3];   
        float 	D_gyro_bias[3]; 
        float   D_acc_bias[3];
        float   D_extra[9];   
                    
        unsigned char	reserve[150];     //预留：置0x00	
}parabag_info_SetFilter,*p_parabag_info_SetFilter;

//陀螺标定因数信息体结构
typedef struct _parabag_info_SetFactorGyro
{
        double 	FactorDyroX;  
        double 	FactorDyroY; 
        double 	FactorDyroZ;                 
              
        unsigned char	reserve[222];     //预留：置0x00	
}parabag_info_SetFactorGyro,*p_parabag_info_SetFactorGyro;

//加计标定因数信息体结构
typedef struct _parabag_info_SetFactorAcc
{
        double 	FactorAccX;  
        double 	FactorAccY; 
        double 	FactorAccZ;                 
              
        unsigned char	reserve[222];     //预留：置0x00	
}parabag_info_SetFactorAcc,*p_parabag_info_SetFactorAcc;


//标定参数信息体结构
typedef struct _parabag_info_SetCalibration
{
        double 	GyroCalibrate[12]; //陀螺标定参数                 
	double 	AccCalibrate[12];  //加计标定参数               
                    
        unsigned char	reserve[54];     //预留：置0x00	
}parabag_info_SetCalibration,*p_parabag_info_SetCalibration;

//温度补偿信息体结构
typedef struct _parabag_info_SetTemOffset
{                  
        unsigned char	reserve[246];     //预留：置0x00	
}parabag_info_SetTemOffset,*p_parabag_info_SetTemOffset;


//设置所有参数信息体结构
typedef struct _parabag_info_SetPara
{
        unsigned short 	SetfrePara;     //输出频率	

        //GNSS杆臂参数(XYZ)
	float 	armX;                   //杆臂X
	float 	armY;                   //杆臂Y
	float 	armZ;                   //杆臂Z

        //天线安装角度(XYZ)
        float 	angleX;                 //X轴角度
	float 	angleY;                 //Y轴角度
	float 	angleZ;                 //Z轴角度

        //惯导-后轮轴中心位置矢量(XYZ)
        float 	vectorX;                //X轴位置矢量
	float 	vectorY;                //Y轴位置矢量
        float 	vectorZ;                //Z轴位置矢量

        //惯导角度安装偏差(XYZ)
        float 	pitch;                  //俯仰角
	float 	roll;                   //横滚角
        float 	Course;                 //航向角

        //坐标轴设置
	unsigned char 	SetCoordPara;	//设置坐标
        unsigned char 	SetDirPara;	//设置方向	

        unsigned short 	SetTimepara;    //静态测零偏时间	

        //GNSS初始值
        float 	GnssInitPitch;          //俯仰角
	float 	GnssInitRoll;           //横滚角
        float 	GnssInitCourse;         //航向角
        float 	GnssInitYaw;            //偏航角
        float 	GnssInitLongitude;      //经度
	float 	GnssInitLatitude;       //纬度
        float 	GnssInitHight;          //高度

        //陀螺标定因数
        double 	FactorDyroX;  
        double 	FactorDyroY; 
        double 	FactorDyroZ;    
        
        //加计标定因数
        double 	FactorAccX;  
        double 	FactorAccY; 
        double 	FactorAccZ; 

        unsigned char	reserve[116];	//预留：置0x00
}parabag_info_SetPara,*p_parabag_info_SetPara;


//接收：参数回读,读取版本号,固化参数,恢复出厂设置共用数据包结构
typedef struct _parabag_pararead															
{
	parabaghead head;           //包头
	
	parabag_info_pararead info; //信息体
	
	parabagend ender;           //包尾
	
}parabag_pararead,*p_parabag_pararead;

//接收：设置类型 是否开启Debug模式，陀螺类型，GPS中断类型，数据输出,软件升级开始,完成，终止命令共用数据包结构
typedef struct _parabag_SetType															
{
	parabaghead head;           //包头
	
	parabag_info_SetType info; //信息体
	
	parabagend ender;           //包尾
	
}parabag_SetType,*p_parabag_SetType;

//接收：设置用户坐标轴数据包结构
typedef struct _parabag_SetCoord															
{
	parabaghead head;           //包头
	
	parabag_info_SetCoord info; //信息体
	
	parabagend ender;           //包尾
	
}parabag_SetCoord,*p_parabag_SetCoord;

//接收：波特率数据包结构
typedef struct _parabag_Setbaud															
{
	parabaghead head;           //包头
	
	parabag_info_Setbaud info; //信息体
	
	parabagend ender;           //包尾
	
}parabag_Setbaud,*p_parabag_Setbaud;

//接收：数据输出频率数据包结构
typedef struct _parabag_Setfrequency															
{
	parabaghead head;           //包头
	
	parabag_info_Setfrequency info; //信息体
	
	parabagend ender;           //包尾
	
}parabag_Setfrequency,*p_parabag_Setfrequency;

//接收：设置静态测零偏时间数据包结构
typedef struct _parabag_SetTime															
{
	parabaghead head;           //包头
	
	parabag_info_SetTime info; //信息体
	
	parabagend ender;           //包尾
	
}parabag_SetTime,*p_parabag_SetTime;

//接收：发送升级包数据包结构
typedef struct _parabag_UpdateSend															
{
	parabaghead head;           //包头
	
	parabag_info_UpdateSend info; //信息体
	
	parabagend ender;           //包尾
	
}parabag_UpdateSend,*p_parabag_UpdateSend;

//接收：设置GNSS杆臂参数数据包结构
typedef struct _parabag_Setgnss															
{
	parabaghead head;           //包头
	
	parabag_info_Setgnss info; //信息体
	
	parabagend ender;           //包尾
	
}parabag_Setgnss,*p_parabag_Setgnss;

//接收：设置天线安装角度数据包结构
typedef struct _parabag_SetAngle															
{
	parabaghead head;           //包头
	
	parabag_info_SetAngle info; //信息体
	
	parabagend ender;           //包尾
	
}parabag_SetAngle,*p_parabag_SetAngle;


//接收：惯导角度安装偏差数据包结构
typedef struct _parabag_SetDeviation															
{
	parabaghead head;               //包头
	
	parabag_info_SetDeviation info; //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetDeviation,*p_parabag_SetDeviation;

//接收：惯导-后轮轴中心位置矢量数据包结构
typedef struct _parabag_SetVector															
{
	parabaghead head;               //包头
	
	parabag_info_SetVector info;    //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetVector,*p_parabag_SetVector;

//接收：GNSS初始值数据包结构
typedef struct _parabag_SetGnssinitval															
{
	parabaghead head;                   //包头
	
	parabag_info_SetGnssinitval info;   //信息体
	
	parabagend ender;                   //包尾
	
}parabag_SetGnssinitval,*p_parabag_SetGnssinitval;

//接收：卡尔曼滤波R矩阵数据包结构
typedef struct _parabag_SetKalmanR															
{
	parabaghead head;               //包头
	
	parabag_info_SetKalmanR info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetKalmanR,*p_parabag_SetKalmanR;

//接收：卡尔曼滤波Q矩阵数据包结构
typedef struct _parabag_SetKalmanQ															
{
	parabaghead head;               //包头
	
	parabag_info_SetKalmanQ info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetKalmanQ,*p_parabag_SetKalmanQ;

//接收：间接滤波校正系数数据包结构
typedef struct _parabag_SetFilter															
{
	parabaghead head;               //包头
	
	parabag_info_SetFilter info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetFilter,*p_parabag_SetFilter;

//接收：陀螺标定因数信息体结构
typedef struct _parabag_SetFactorGyro															
{
	parabaghead head;               //包头
	
	parabag_info_SetFactorGyro info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetFactorGyro,*p_parabag_SetFactorGyro;

//接收：加计标定因数信息体结构
typedef struct _parabag_SetFactorAcc															
{
	parabaghead head;               //包头
	
	parabag_info_SetFactorAcc info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetFactorAcc,*p_parabag_SetFactorAcc;

//接收：标定参数数据包结构
typedef struct _parabag_SetCalibration															
{
	parabaghead head;               //包头
	
	parabag_info_SetCalibration info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetCalibration,*p_parabag_SetCalibration;


//接收：温度补偿数据包结构
typedef struct _parabag_SetTemOffset															
{
	parabaghead head;               //包头
	
	parabag_info_SetTemOffset info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetTemOffset,*p_parabag_SetTemOffset;


//接收：设置所有参数据包结构
typedef struct _parabag_SetPara															
{
	parabaghead head;               //包头
	
	parabag_info_SetPara info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_SetPara,*p_parabag_SetPara;





//--------下位机发送信息结构体-----------------
//软件升级开始命令下位机反馈信息体
typedef struct _parabag_info_UpdateStart_back
{
        unsigned char	BackFlag;   //反馈标志(0x01-正常/02-异常)

        //版本信息
        unsigned char Vermain;      //主版本号
        unsigned char Verminor;      //次版本号
        unsigned char Revision;      //修订号
        unsigned int  VerDate;       //时间，年月日	
        unsigned char Plan;          //方案（ GD-0，先辑-1，芯驰-2，英飞凌-3，...）
        unsigned char Suffix;        //后缀  （内测版-0，公测版-1，候选发布版-2，正式版-3，...）
        unsigned char Custom;       //客户定制版（0~254），通用版：255	

        unsigned short 	Count;      //帧计数	
        
        unsigned char	reserve[41];	//预留：置0x00						
}parabag_info_UpdateStart_back,*p_parabag_info_UpdateStart_back;


//发送升级包命令下位机反馈信息体
typedef struct _parabag_info_UpdateSend_back
{
        unsigned char	BackFlag;     //反馈标志(0x01-正常/02-异常)
	unsigned short 	BaoIndex;     //当前包编号 0,1,2,3,........
        unsigned short 	Count;        //帧计数	
        
        unsigned char	reserve[49];  //预留：置0x00						
}parabag_info_UpdateSend_back,*p_parabag_info_UpdateSend_back;

//软件升级完成命令下位机反馈信息体
typedef struct _parabag_info_UpdateEnd_back
{
        unsigned char	BackFlag;   //反馈标志(0x01-正常/02-异常)
        
        unsigned char	reserve[53];	//预留：置0x00						
}parabag_info_UpdateEnd_back,*p_parabag_info_UpdateEnd_back;

//软件升级终止命令下位机反馈信息体
typedef struct _parabag_info_UpdateStop_back
{
        unsigned char	BackFlag;   //反馈标志(0x01-正常/02-异常)
        
        unsigned char	reserve[53];	//预留：置0x00						
}parabag_info_UpdateStop_back,*p_parabag_info_UpdateStop_back;

//参数回读命令0(输出频率,GNSS杆臂参数)下位机反馈信息体
typedef struct _parabag_info_ReadPara0_back
{
        unsigned char	BackFlag;   //反馈标志(0x01-正常/02-异常)

        //输出频率	
        unsigned short 	SetfrePara;     //输出频率	

        //GNSS杆臂参数(XYZ)
	float 	armX;                   //杆臂X
	float 	armY;                   //杆臂Y
	float 	armZ;                   //杆臂Z
        
        unsigned char	reserve[39];	//预留：置0x00						
}parabag_info_ReadPara0_back,*p_parabag_info_ReadPara0_back;

//参数回读命令1(天线安装角度,惯导-后轮轴中心位置矢量)下位机反馈信息体
typedef struct _parabag_info_ReadPara1_back
{
        unsigned char	BackFlag;   //反馈标志(0x01-正常/02-异常)

        //天线安装角度(XYZ)
        float 	angleX;                 //X轴角度
	float 	angleY;                 //Y轴角度
	float 	angleZ;                 //Z轴角度

        //惯导-后轮轴中心位置矢量(XYZ)
        float 	vectorX;                //X轴位置矢量
	float 	vectorY;                //Y轴位置矢量
        float 	vectorZ;                //Z轴位置矢量
        
        unsigned char	reserve[29];	//预留：置0x00						
}parabag_info_ReadPara1_back,*p_parabag_info_ReadPara1_back;

//参数回读命令2(惯导角度安装偏差,坐标轴设置)下位机反馈信息体
typedef struct _parabag_info_ReadPara2_back
{
        unsigned char	BackFlag;   //反馈标志(0x01-正常/02-异常)

        //惯导角度安装偏差(XYZ)
        float 	pitch;                  //俯仰角
	float 	roll;                   //横滚角
        float 	Course;                 //航向角

        //坐标轴设置
	unsigned char 	SetCoordPara;	//设置坐标
        unsigned char 	SetDirPara;	//设置方向	
        
        unsigned char	reserve[39];	//预留：置0x00						
}parabag_info_ReadPara2_back,*p_parabag_info_ReadPara2_back;

//参数回读命令3(静态测零偏时间,GNSS初始值)下位机反馈信息体
typedef struct _parabag_info_ReadPara3_back
{
        unsigned char	BackFlag;   //反馈标志(0x01-正常/02-异常)

        //静态测零偏时间
        unsigned short 	SetTimepara;    //静态测零偏时间	

        //GNSS初始值
        float 	GnssInitpitch;                  //俯仰角
	float 	GnssInitroll;                   //横滚角
        float 	GnssInitCourse;                 //航向角
        float 	GnssInityaw;                    //偏航角
        float 	GnssInitlongitude;              //经度
	float 	GnssInitlatitude;               //纬度
	float 	GnssInithight;                  //高度
        
        unsigned char	reserve[23];            //预留：置0x00						
}parabag_info_ReadPara3_back,*p_parabag_info_ReadPara3_back;

//参数回读命令4(陀螺加计标定因数)下位机反馈信息体
typedef struct _parabag_info_ReadPara4_back
{
        unsigned char	BackFlag;   //反馈标志(0x01-正常/02-异常)

        //陀螺标定因数
        double 	FactorDyroX;  
        double 	FactorDyroY; 
        double 	FactorDyroZ;    
        
        //加计标定因数
        double 	FactorAccX;  
        double 	FactorAccY; 
        double 	FactorAccZ; 
        
        unsigned char	reserve[5];            //预留：置0x00						
}parabag_info_ReadPara4_back,*p_parabag_info_ReadPara4_back;


//参数回读命令下位机反馈信息体(根据上位机命令选择发送)
typedef struct _parabag_info_pararead_back													
{
        unsigned char	BackFlag;       //反馈标志(0x01-正常/02-异常)

        //输出频率	
        unsigned short 	SetfrePara;     //输出频率	

        //GNSS杆臂参数(XYZ)
	float 	armX;                   //杆臂X
	float 	armY;                   //杆臂Y
	float 	armZ;                   //杆臂Z

        //天线安装角度(XYZ)
        float 	angleX;                 //X轴角度
	float 	angleY;                 //Y轴角度
	float 	angleZ;                 //Z轴角度

        //惯导-后轮轴中心位置矢量(XYZ)
        float 	vectorX;                //X轴位置矢量
	float 	vectorY;                //Y轴位置矢量
        float 	vectorZ;                //Z轴位置矢量

        //惯导角度安装偏差(XYZ)
        float 	pitch;                  //俯仰角
	float 	roll;                   //横滚角
        float 	Course;                 //航向角

        //坐标轴设置
	unsigned char 	SetCoordPara;	//设置坐标
        unsigned char 	SetDirPara;	//设置方向	

        //静态测零偏时间
        unsigned short 	SetTimepara;    //静态测零偏时间	

        //GNSS初始值
        float 	GnssInitpitch;                  //俯仰角
	float 	GnssInitroll;                   //横滚角
        float 	GnssInitCourse;                 //航向角
        float 	GnssInityaw;                    //偏航角
        float 	GnssInitlongitude;              //经度
	float 	GnssInitlatitude;               //纬度
	float 	GnssInithight;                  //高度
        															
	unsigned char	reserve[10];//预留：置0x00
}parabag_info_pararead_back,*p_parabag_info_pararead_back;


//其他命令下位机反馈信息体
typedef struct _parabag_info_Other_back
{
        unsigned char	BackFlag;   //反馈标志(0x01-正常/02-异常)
        
        unsigned char	reserve[53];	//预留：置0x00						
}parabag_info_Other_back,*p_parabag_info_Other_back;



//发送：软件升级开始命令下位机反馈数据包结构
typedef struct _parabag_UpdateStart_back															
{
	parabaghead head;               //包头
	
	parabag_info_UpdateStart_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_UpdateStart_back,*p_parabag_UpdateStart_back;

//发送：发送升级包命令下位机反馈数据包结构
typedef struct _parabag_UpdateSend_back															
{
	parabaghead head;               //包头
	
	parabag_info_UpdateSend_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_UpdateSend_back,*p_parabag_UpdateSend_back;


//发送：软件升级完成命令下位机反馈数据包结构
typedef struct _parabag_UpdateEnd_back															
{
	parabaghead head;               //包头
	
	parabag_info_UpdateEnd_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_UpdateEnd_back,*p_parabag_UpdateEnd_back;


//发送：软件升级终止命令下位机反馈数据包结构
typedef struct _parabag_UpdateStop_back															
{
	parabaghead head;               //包头
	
	parabag_info_UpdateStop_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_UpdateStop_back,*p_parabag_UpdateStop_back;


//发送：参数回读命令0(输出频率,GNSS杆臂参数)下位机反馈数据包结构
typedef struct _parabag_ReadPara0_back															
{
	parabaghead head;               //包头
	
	parabag_info_ReadPara0_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_ReadPara0_back,*p_parabag_ReadPara0_back;

//发送：参数回读命令1(天线安装角度,惯导-后轮轴中心位置矢量)下位机反馈数据包结构
typedef struct _parabag_ReadPara1_back															
{
	parabaghead head;               //包头
	
	parabag_info_ReadPara1_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_ReadPara1_back,*p_parabag_ReadPara1_back;

//发送：参数回读命令2(惯导角度安装偏差,坐标轴设置)下位机反馈数据包结构
typedef struct _parabag_ReadPara2_back															
{
	parabaghead head;               //包头
	
	parabag_info_ReadPara2_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_ReadPara2_back,*p_parabag_ReadPara2_back;

//发送：参数回读命令3(静态测零偏时间,GNSS初始值)下位机反馈数据包结构
typedef struct _parabag_ReadPara3_back															
{
	parabaghead head;               //包头
	
	parabag_info_ReadPara3_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_ReadPara3_back,*p_parabag_ReadPara3_back;

//发送：参数回读命令4(陀螺加计标定因数)下位机反馈数据包结构
typedef struct _parabag_ReadPara4_back															
{
	parabaghead head;               //包头
	
	parabag_info_ReadPara4_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_ReadPara4_back,*p_parabag_ReadPara4_back;


//发送：其他命令下位机反馈数据包结构
typedef struct _parabag_Other_back															
{
	parabaghead head;               //包头
	
	parabag_info_Other_back info;   //信息体
	
	parabagend ender;               //包尾
	
}parabag_Other_back,*p_parabag_Other_back;




//参数设置数据结构体
typedef struct _Setpara_Data													
{
        unsigned char 	SetDebugMode;    //是否开启Debug模式 0x01-开启 0x00-关闭														
        unsigned char 	SetGyroType;     //陀螺类型 0x01-FOG  0x00-MEMS
        unsigned char 	SetGpsType;      //GPS中断类型 0x00-GPS融合, 0x01-运动学模型, 0x02-运动学模型+轮速, 0x03-断开GPS
        unsigned char 	SetDataOutType;  //数据输出类型 0-输出组合导航数据，1-输出组合导航+IMU+GPS数据，255-暂停输出

        unsigned char 	SetCoord;	//设置坐标 0-x,y,z 1-x,z,y 2-y,x,z 3-y,z,x 4-z,x,y 5-z,y,x
        unsigned char 	SetDir;         //设置方向 0-正,正,正 1-反,正,正 2-正,反,正 3-正,正,反 4-反,反,正 5-反,正,反 6-正,反,反 7-反,反,反

        unsigned short 	Setbaud;        //设置波特率	
        unsigned short 	Setfre;         //输出频率	
        unsigned short 	SetTime;        //静态测零偏时间	

        //设置GNSS杆臂参数
        float 	armX;                   //杆臂X
        float 	armY;                   //杆臂Y
        float 	armZ;                   //杆臂Z

        //设置天线安装角度
        float 	angleX;                 //X轴角度
        float 	angleY;                 //Y轴角度
        float 	angleZ;                 //Z轴角度

        //惯导角度安装偏差
        float 	pitch;                  //俯仰角
        float 	roll;                   //横滚角
        float 	Course;                 //航向角

        //惯导-后轮轴中心位置矢量
        float 	vectorX;                //X轴位置矢量
        float 	vectorY;                //Y轴位置矢量
        float 	vectorZ;                //Z轴位置矢量

        //GNSS初始值
        float 	GnssInitPitch;          //俯仰角
        float 	GnssInitRoll;           //横滚角
        float 	GnssInitCourse;         //航向角
        float 	GnssInitYaw;            //偏航角
        float 	GnssInitLongitude;      //经度
        float 	GnssInitLatitude;       //纬度
        float 	GnssInitHight;          //高度

        //卡尔曼滤波R矩阵
        float 	R_heading;                  
        float 	R_velocity[3];                  
        float 	R_position[3];   
        float 	R_extra[9];   

        //卡尔曼滤波Q矩阵
        float 	Q_phi[3];                  
        float 	Q_velocity[3];                  
        float 	Q_position[3];   
        float 	Q_gyro_bias[3]; 
        float   Q_acc_bias[3];
        float   Q_extra[9]; 
        
        //间接滤波校正系数
        float 	D_phi[3];                  
        float 	D_velocity[3];                  
        float 	D_position[3];   
        float 	D_gyro_bias[3]; 
        float   D_acc_bias[3];
        float   D_extra[9]; 
        
        //陀螺标定因数
        double 	FactorDyroX;  
        double 	FactorDyroY; 
        double 	FactorDyroZ;    
        
        //加计标定因数
        double 	FactorAccX;  
        double 	FactorAccY; 
        double 	FactorAccZ; 
        
        //标定参数
        double 	GyroCalibrate[12]; //陀螺标定参数                 
        double 	AccCalibrate[12];  //加计标定参数   

        unsigned short Flag;//区分rs232还是rs422的标志位

}Setpara_Data,*p_Setpara_Data;

#pragma pack()

//是否开启Debug模式枚举
typedef enum{
	DEBUG_CLOSE	=0x00,//关闭Debug模式
        DEBUG_OPEN	=0x01,//开启Debug模式
}SetDebugMode_e;

//陀螺类型枚举
typedef enum{
	MEMS_TYPE	=0x00,//MEMS
        FOG_TYPE	=0x01,//FOG
}SetGyroType_e;

//GPS中断类型枚举
typedef enum{
	FUSE_GPS	=0x00,//GPS融合
        SPORTS_GPS	=0x01,//运动学模型
        SPORTS_WHEEL_GPS=0x02,//运动学模型+轮速
        CLOSE_GPS	=0x03,//断开GPS
}SetGpsType_e;

//数据输出类型枚举
typedef enum{
	INS_TYPE	=0x00,//输出组合导航数据
        INS_IMU_GPS_TYPE=0x01,//输出组合导航+IMU+GPS数据
        CLOSE_OUT	=0xFF,//暂停输出
}SetDataOutType_e;

//设置坐标枚举
typedef enum{
	SETCOORD_X_Y_Z	=0x00,//x,y,z
        SETCOORD_X_Z_Y	=0x01,//x,z,y
        SETCOORD_Y_X_Z	=0x02,//y,x,z
        SETCOORD_Y_Z_X	=0x03,//y,z,x
        SETCOORD_Z_X_Y	=0x04,//z,x,y
        SETCOORD_Z_Y_X	=0x05,//z,y,x
}SetCoord_e;

//设置方向枚举
typedef enum{
	SETDIR_1_1_1	=0x00,//正,正,正
        SETDIR_0_1_1	=0x01,//反,正,正
        SETDIR_1_0_1	=0x02,//正,反,正
        SETDIR_1_1_0	=0x03,//正,正,反
        SETDIR_0_0_1	=0x05,//反,反,正
        SETDIR_0_1_0	=0x06,//反,正,反
        SETDIR_1_0_0	=0x07,//正,反,反
        SETDIR_0_0_0	=0x08,//反,反,反
}SetDir_e;

typedef struct _dmauart {												
												
	uint8_t	rxbuffer[FRAMEPARSEBUFSIZE] ;
  
} dmauart_t,*p_dmauart_t;

extern Setpara_Data stSetPara;

//读取参数
void ReadParaFromFlash(void);
//固件升级处理
void ParaUpdateHandle(uint8_t *pucBuf,uint16_t usIndex,uint16_t usTotalBao,uint8_t ucLen);

void UartDmaRecSetPara(p_dmauart_t pdmauart);



#endif