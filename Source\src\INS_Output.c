#include "appmain.h"
#include "nav_includes.h"
#include "frame_analysis.h"
#include "gdtypedefine.h"
#include "INS_Data.h"
#include "bsp_flash.h"
#include "INS_Output.h"

extern u8 g_StartUpdateFirm;//开始升级标志 1:开始升级 0:结束升级


void protocol_opticalgyro(void);
unsigned int syncount = 0;
float gfog0;
void NAV_Output(void)
{
  if(g_StartUpdateFirm==1)
  {
    return;
  }
  
	if (gsystemflag & c_systemflag_rtkoff) {
		memcpy(&hGPSData, &hGPSData01, sizeof(hGPSData));
	}

	
	hINSCANData.data_stream.accelX = (combineData.scha634Info.acc_x + 6) / 0.00018310546875;
	hINSCANData.data_stream.accelY = (combineData.scha634Info.acc_y + 6) / 0.00018310546875;
	hINSCANData.data_stream.accelZ = (combineData.scha634Info.acc_z + 6) / 0.00018310546875;
	hINSCANData.data_stream.gyroX = (combineData.scha634Info.gyro_x + 300) / 0.0091552734375;
	hINSCANData.data_stream.gyroY = (combineData.scha634Info.gyro_y + 300) / 0.0091552734375;
	hINSCANData.data_stream.gyroZ = (combineData.scha634Info.gyro_z + 300) / 0.0091552734375;
	
	hINSCANData.data_stream.roll = (NAV_Data_Out.roll + 360) / 0.010986; 
	hINSCANData.data_stream.pitch = (NAV_Data_Out.pitch + 360) / 0.010986;
	hINSCANData.data_stream.azimuth = (NAV_Data_Out.heading + 360) / 0.010986;

	//                        
	//hINSCANData.data_stream.latitude = (NAV_Data_Full.SINS.pos[0] * RAD2DEG + 180) / 0.0000001;
	//hINSCANData.data_stream.longitude = (NAV_Data_Full.SINS.pos[1] * RAD2DEG + 180) / 0.0000001;
	hINSCANData.data_stream.latitude = (NAV_Data_Full.SINS.pos[0] * RAD2DEG + 180) / 0.0000001;
	hINSCANData.data_stream.longitude = (NAV_Data_Full.SINS.pos[1] * RAD2DEG + 180) / 0.0000001;
	hINSCANData.data_stream.altitude = (NAV_Data_Full.SINS.pos[2] + 10000) / 0.001;
	hINSCANData.data_stream.gpssecond = NAV_Data_Full.GPS.gpssecond;

	hINSCANData.data_stream.ve = (NAV_Data_Full.SINS.vn[0] + 100) / 0.0030517;
	hINSCANData.data_stream.vn = (NAV_Data_Full.SINS.vn[1] + 100) / 0.0030517;
	hINSCANData.data_stream.vu = (NAV_Data_Full.SINS.vn[2] + 100) / 0.0030517;

	hINSCANData.data_stream.std_lat = NAV_Data_Full.KF.Xk[6] * RE_WGS84 / 0.001;
	hINSCANData.data_stream.std_lon = NAV_Data_Full.KF.Xk[7]* cos(NAV_Data_Full.SINS.pos[0]) *Rp_WGS84 / 0.001;
	hINSCANData.data_stream.std_height = NAV_Data_Full.KF.Xk[8] / 0.001;
	hINSCANData.data_stream.std_heading = NAV_Data_Full.KF.Xk[2] * RAD2DEG / 0.001;

	hINSCANData.data_stream.std_ve = NAV_Data_Full.KF.Xk[3] / 0.001;          
	hINSCANData.data_stream.std_vn = NAV_Data_Full.KF.Xk[4] / 0.001;
	hINSCANData.data_stream.std_vu = NAV_Data_Full.KF.Xk[5] / 0.001;

	hINSCANData.data_stream.std_roll = NAV_Data_Full.KF.Xk[1] * RAD2DEG / 0.001;                        
	hINSCANData.data_stream.std_pitch = NAV_Data_Full.KF.Xk[0] * RAD2DEG / 0.001;
	hINSCANData.data_stream.devicetemperature = combineData.scha634Info.temp_uno / 0.01;                        

	//status...
	hINSCANData.data_stream.GpsFlag_Pos = NAV_Data_Full.GPS.rtkStatus;
	hINSCANData.data_stream.NumSV = combineData.gnssInfo.StarNum;
	hINSCANData.data_stream.GpsFlag_heading = NAV_Data_Full.GPS.headingStatus;
	hINSCANData.data_stream.Car_Status = NAV_Data_Full.ODS.ods_flag;
	hINSCANData.data_stream.Gps_Age = 0;
	hINSCANData.data_stream.gps_week = combineData.gnssInfo.gpsweek;
	hINSCANData.data_stream.INS_status = NAV_Data_Full.Nav_Status;
	
	hINSCANData.data_stream.std_lat = 0.0 / 0.001;
	hINSCANData.data_stream.std_lon = 0.0 / 0.001;
	hINSCANData.data_stream.std_height =  0.0 / 0.001;
	hINSCANData.data_stream.std_heading = 0.0 / 0.001;
	
	hINSCANData.data_stream.Gear = NAV_Data_Full.ODS.Gear;
	hINSCANData.data_stream.WheelSpeed = NAV_Data_Full.ODS.WheelSpeed_ave;
	hINSCANData.data_stream.gps_week = NAV_Data_Full.GPS.gpsweek;
	


//			hINSCANData.data_stream.status = ;
//			hINSCANData.data_stream.Car_Status = ;
//			hINSCANData.data_stream.INS_status = ;
//			hINSCANData.data_stream.Nav_Standard_flag = ;
//			hINSCANData.data_stream.INS_States = ;

//			hINSCANData.data_stream.reserved[6] = ;
//			hINSCANData.data_stream.poll_frame = ;
//			hINSCANData.data_stream.type = ;
//			hINSCANData.data_stream.xor_verify1 = ;
//			hINSCANData.data_stream.xor_verify2 = ;
//			hINSCANData.data_stream.GpsFlag_Pos = ;
//			hINSCANData.data_stream.NumSV = ;
//			hINSCANData.data_stream.GpsFlag_heading = ;
//			hINSCANData.data_stream.Gps_Age = ;


	//uart4sendmsg((char*)"hello word...%d\r\n", strlen("hello word...%d\r\n"));
	//#if c_output_normal
	if ((goutputmode & c_outputmode_normal) || (goutputmode & c_outputmode_testing)) {
		protocol_send();
		mcusendtopcdriversdata(0, 0, 0);
	}
	if (goutputmode & c_outputmode_gdw) {
		protocol_send();
		mcusendtopcdriversdata(0, 0, 0);
	}
	
	//#endif
	//#if c_output_testfog
	if (goutputmode & c_outputmode_testfog) {
		protocol_opticalgyro();
	}
	//#endif
	//IMU_test_data_send(&NAV_Data_Out); 
	syncount++;
	#if !c_systemrunmode_cantooling
	//if (syncount % 2) {      //输出频率200Hz
  //if ((syncount % 20)==0) {  //输出频率20Hz
  
    //输出频率设置
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / comm_read_currentFreq());
    tCnt++;
    if(tCnt >= freq)
    {
        tCnt = 0;
	#else
	if (0) {
	#endif
#if  0
		#if 1
		unsigned int looptimes = 0, looptimestimeout = 10000;
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_acc(&hCAN0);	//CAN_ID_INS_ACC   0x500
		can_transmit_gyro(&hCAN0);	//CAN_ID_INS_GYRO  0x501
		can_transmit_angle(&hCAN0); //CAN_ID_INS_ANGLE 0x502
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_h(&hCAN0);		//CAN_ID_INS_HEIGHT    0x503
		can_transmit_pos(&hCAN0);	//CAN_ID_INS_POSITION  0x504
		can_transmit_speed(&hCAN0);	//CAN_ID_INS_SPEED     0x505
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_status(&hCAN0);		//CAN_ID_INS_STATUS  		0x506
		can_transmit_std_heading(&hCAN0);	//CAN_ID_INS_STD_HEADING	0x507
		can_transmit_temperature(&hCAN0);   //CAN_ID_INS_STD_SPEED 		0x508
		#else
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_acc(&hCAN0);	//CAN_ID_INS_ACC   0x500
		can_transmit_gyro(&hCAN0);	//CAN_ID_INS_GYRO  0x501
		can_transmit_angle(&hCAN0); //CAN_ID_INS_ANGLE 0x502
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_h(&hCAN0);		//CAN_ID_INS_HEIGHT    0x503
		can_transmit_pos(&hCAN0);	//CAN_ID_INS_POSITION  0x504
		can_transmit_speed(&hCAN0);	//CAN_ID_INS_SPEED     0x505
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_status(&hCAN0);		//CAN_ID_INS_STATUS  		0x506
		can_transmit_std_heading(&hCAN0);	//CAN_ID_INS_STD_HEADING	0x507
		can_transmit_std_speed(&hCAN0);   	//CAN_ID_INS_STD_SPEED 		0x508
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_std_pose(&hCAN0);   	//CAN_ID_INS_STD_POSE 		0x509
		while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		//can_transmit_posatemp(&hCAN0);	//CAN_ID_INS_POSaTEMP 0x509
		#endif
#else
#if 1
#if 1
#if c_output_enable_can
		unsigned int looptimes = 0, looptimestimeout = 10000;
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_acc(&hCAN0);	//CAN_ID_INS_ACC   0x500
		can_transmit_gyro(&hCAN0);	//CAN_ID_INS_GYRO  0x501
		can_transmit_angle(&hCAN0); //CAN_ID_INS_ANGLE 0x502
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_h(&hCAN0);		//CAN_ID_INS_HEIGHT    0x503
		can_transmit_pos(&hCAN0);	//CAN_ID_INS_POSITION  0x504
		can_transmit_speed(&hCAN0);	//CAN_ID_INS_SPEED     0x505
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME0));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME1));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN0.canDev,CAN_FLAG_TME2));
		can_transmit_status(&hCAN0);		//CAN_ID_INS_STATUS  		0x506
		can_transmit_std_heading(&hCAN0);	//CAN_ID_INS_STD_HEADING	0x507
		can_transmit_temperature(&hCAN0);  	//CAN_ID_INS_STD_SPEED 		0x508
#endif
#else
		unsigned int looptimes = 0, looptimestimeout = 10000;
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME0));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME1));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME2));
		can_transmit_acc(&hCAN1);	//CAN_ID_INS_ACC   0x500
		can_transmit_gyro(&hCAN1);	//CAN_ID_INS_GYRO  0x501
		can_transmit_angle(&hCAN1); //CAN_ID_INS_ANGLE 0x502
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME0));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME1));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME2));
		can_transmit_h(&hCAN1);		//CAN_ID_INS_HEIGHT    0x503
		can_transmit_pos(&hCAN1);	//CAN_ID_INS_POSITION  0x504
		can_transmit_speed(&hCAN1);	//CAN_ID_INS_SPEED     0x505
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME0));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME1));
		looptimes = 0;	do {if (looptimes++ > looptimestimeout) break;}while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME2));
		can_transmit_status(&hCAN1);		//CAN_ID_INS_STATUS  		0x506
		can_transmit_std_heading(&hCAN1);	//CAN_ID_INS_STD_HEADING	0x507
		can_transmit_temperature(&hCAN1);  	//CAN_ID_INS_STD_SPEED 		0x508
#endif
#else
		while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME0));
		while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME1));
		while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME2));
		can_transmit_acc(&hCAN1);	//CAN_ID_INS_ACC   0x500
		can_transmit_gyro(&hCAN1);	//CAN_ID_INS_GYRO  0x501
		can_transmit_angle(&hCAN1); //CAN_ID_INS_ANGLE 0x502
		while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME0));
		while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME1));
		while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME2));
		can_transmit_h(&hCAN1);		//CAN_ID_INS_HEIGHT    0x503
		can_transmit_pos(&hCAN1);	//CAN_ID_INS_POSITION  0x504
		can_transmit_speed(&hCAN1);	//CAN_ID_INS_SPEED     0x505
		while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME0));
		while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME1));
		while(SET != can_flag_get(hCAN1.canDev,CAN_FLAG_TME2));
		can_transmit_status(&hCAN1);		//CAN_ID_INS_STATUS  		0x506
		can_transmit_std_heading(&hCAN1);	//CAN_ID_INS_STD_HEADING	0x507
		can_transmit_temperature(&hCAN1);  	//CAN_ID_INS_STD_SPEED 		0x508
#endif
#endif
	}
}



void protocol_opticalgyro(void)
{
	unsigned char xord;
	opticalgyroprotocol_t	opticalgyrod;
	opticalgyrod.head0 = 0xa5;
	opticalgyrod.head1 = 0xa5;
	opticalgyrod.data[0] = 0x11;
	opticalgyrod.data[1] = 0x12;
	opticalgyrod.data[2] = 0x13;
	opticalgyrod.data[3] = 0x14;
	opticalgyrod.data[4] = 0x15;
	opticalgyrod.data[5] = 0x16;
	
	opticalgyrod.data[0] = 0x00;
	opticalgyrod.data[1] = 0x00;
	opticalgyrod.data[2] = 0x0c;
	opticalgyrod.data[3] = 0xff;
	opticalgyrod.data[4] = 0x01;
	opticalgyrod.data[5] = 0xbe;
	
	xord = 0;
	for (int i = 0; i < 6; i++) {
		xord ^= opticalgyrod.data[i];
	}
	opticalgyrod.xord = xord;
	uart4sendmsg((char*)&opticalgyrod, sizeof(opticalgyrod));
}


		

