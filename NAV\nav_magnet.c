/***********************************************************************************
nav magnet module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-9          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"


void Get_Magnet_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
	unsigned short i = 0;
	//Magnet数据
	for(i = 0;i<3;i++)
	{
		NAV_Data_Full_p->MAGNET.mag_use[i] = CombineData_p->magInfo.magGrp[i];
	}	
}
	
//初始化计算航向
//参考软件设计说明书18.1	AHRS互补滤波(Mahony)算法
double MagInitHeading(double *Mag, double *att,double *pos)
{
	double tmp1,tmp2;
	tmp1	=	Mag[0]*cos(att[1])+Mag[2]*sin(att[1]);
	tmp2	=	Mag[1]*cos(att[0])+Mag[0]*sin(att[0])*sin(att[1])-Mag[2]*sin(att[0]*cos(att[1]));
	
	att[2]	=	atan2(tmp1,tmp2);

	return 0.0;
}

//处理磁场数据
unsigned char ProMagData()
{
	return 0;
}

//磁北转真北，如果需要可以WMM，参考代码WMM2020_Linux
double  CalMagDeclination(double heading, double *pos)
{
	return heading;
}



