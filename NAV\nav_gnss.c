/***********************************************************************************
nav gnss module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-22          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"

unsigned char gps_data_check(_NAV_Data_Full_t* NAV_Data_Full_p);
//double gps_pre_location[2]={0};//存上一时刻纬经度


unsigned char is_gnss_update(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	if(NAV_Data_Full_p->GPS.gpssecond982_old != NAV_Data_Full_p->GPS.gpssecond982)
	{
		return RETURN_SUCESS;
	}
	return RETURN_FAIL;
}
/******************************************************************************
*原  型：u8 gps_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：gps数据检测
*输  入：无
*输  出：无
*******************************************************************************/
unsigned char gps_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned char abnormal_flag = RETURN_SUCESS;
	int difftime=0;
	difftime = NAV_Data_Full_p->GPS.gpssecond-NAV_Data_Full_p->GPS.gpssecond_old;

	if(E_NAV_STATUS_START!=NAV_Data_Full_p->Nav_Status)
	{
		if((difftime<0)||(difftime>(1000/SAMPLE_FREQ_GNSS)))
		{
#ifdef linux
			inav_log(INAVMD(LOG_ERR),"gps_data_check:gpssecond=%d,gpssecond_old=%d\r\n"\
			,NAV_Data_Full_p->GPS.gpssecond,NAV_Data_Full_p->GPS.gpssecond_old);
#endif
			abnormal_flag = RETURN_FAIL;
		}
	}
	return abnormal_flag;
}

void Get_GNSS_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
	//GPS
	NAV_Data_Full_p->GPS.gpssecond_old = NAV_Data_Full_p->GPS.gpssecond;
	NAV_Data_Full_p->GPS.gpssecond982_old = NAV_Data_Full_p->GPS.gpssecond982;
	NAV_Data_Full_p->GPS.gpssecond = CombineData_p->gnssInfo.gpssecond;//ms
 NAV_Data_Full_p->GPS.gpssecond982 = CombineData_p->gnssInfo.gpssecond982;
	//if(NAV_Data_Full_p->GPS.gpssecond982_old != CombineData_p->gnssInfo.gpssecond982)
	//if(fabs(NAV_Data_Full_p->GPS.gpssecond982_old - CombineData_p->gnssInfo.gpssecond982)<10)
	{
//		gps_pre_location[0]=NAV_Data_Full_p->GPS.Lat;
//		gps_pre_location[1]=NAV_Data_Full_p->GPS.Lon;
		//
			//****************gnss数据更新判断**********
	if(is_gnss_update(NAV_Data_Full_p))  
	{
		NAV_Data_Full_p->GPS.gps_up_flag = E_GPS_IS_UPDATE;
		//
		NAV_Data_Full_p->GPS.GPS_UP_Pre.Lat = NAV_Data_Full_p->GPS.Lat ;
  NAV_Data_Full_p->GPS.GPS_UP_Pre.Lon = NAV_Data_Full_p->GPS.Lon ;
		NAV_Data_Full_p->GPS.GPS_UP_Pre.Altitude = NAV_Data_Full_p->GPS.Altitude ;
		NAV_Data_Full_p->GPS.GPS_UP_Pre.ve = NAV_Data_Full_p->GPS.ve ;
  NAV_Data_Full_p->GPS.GPS_UP_Pre.vn = NAV_Data_Full_p->GPS.vn ;
		NAV_Data_Full_p->GPS.GPS_UP_Pre.vu = NAV_Data_Full_p->GPS.vu ;
		NAV_Data_Full_p->GPS.GPS_UP_Pre.Heading = NAV_Data_Full_p->GPS.Heading ;
		NAV_Data_Full_p->GPS.GPS_UP_Pre.Heading_cor = NAV_Data_Full_p->GPS.Heading_cor ;//-180~180
		NAV_Data_Full_p->GPS.GPS_UP_Pre.gpssecond982_old = NAV_Data_Full_p->GPS.gpssecond982_old ;
		NAV_Data_Full_p->GPS.GPS_UP_Pre.Position_Status = NAV_Data_Full_p->GPS.Position_Status ;
		NAV_Data_Full_p->GPS.GPS_UP_Pre.rtkStatus = NAV_Data_Full_p->GPS.rtkStatus ;
		NAV_Data_Full_p->GPS.GPS_UP_Pre.headingStatus = NAV_Data_Full_p->GPS.headingStatus ;//**********
	}
	else
	{
		NAV_Data_Full_p->GPS.gps_up_flag = E_GPS_NO_UPDATE;
	}

/************************************判断当前位置所在半球*******************************************/	
		if(E_LAT_SOUTH_Hemisphere == CombineData_p->gnssInfo.LatHemisphere)
		{
			NAV_Data_Full_p->GPS.Lat = -CombineData_p->gnssInfo.Lat;
		}
		else
		{
			NAV_Data_Full_p->GPS.Lat = CombineData_p->gnssInfo.Lat;
		}
		
		if(E_LON_WEST_Hemisphere == CombineData_p->gnssInfo.LonHemisphere)
		{
			NAV_Data_Full_p->GPS.Lon = -CombineData_p->gnssInfo.Lon;
		}
		else
		{
			NAV_Data_Full_p->GPS.Lon = CombineData_p->gnssInfo.Lon;
		}
		
		NAV_Data_Full_p->GPS.Altitude = CombineData_p->gnssInfo.Altitude;
		NAV_Data_Full_p->GPS.ve = CombineData_p->gnssInfo.ve;
		NAV_Data_Full_p->GPS.vn = CombineData_p->gnssInfo.vn;
		NAV_Data_Full_p->GPS.vu = CombineData_p->gnssInfo.vu;
		NAV_Data_Full_p->GPS.Pitch = CombineData_p->gnssInfo.Pitch;
		NAV_Data_Full_p->GPS.Roll = CombineData_p->gnssInfo.Roll;
		NAV_Data_Full_p->GPS.Heading = CombineData_p->gnssInfo.Heading;
		NAV_Data_Full_p->GPS.trackTrue = CombineData_p->gnssInfo.trackTrue;
		NAV_Data_Full_p->GPS.Position_Status = CombineData_p->gnssInfo.PositioningState;   
		NAV_Data_Full_p->GPS.Heading_cor = -CorrHeading(NAV_Data_Full_p->GPS.Heading-NAV_Data_Full_p->Param.gnssAtt_from_vehicle[2]);//*******-180~180****;
		NAV_Data_Full_p->GPS.baseline = CombineData_p->gnssInfo.baseline;
		//NAV_Data_Full_p->GPS.rtkStatus = CombineData_p->gnssInfo.rtkStatus;
		//kinematic 定位rtk状态
		SetNavRtkStatus(CombineData_p->gnssInfo.rtkStatus);
		//movingbase 定向rtk状态
		NAV_Data_Full_p->GPS.headingStatus = CombineData_p->gnssInfo.headingStatus;
		NAV_Data_Full_p->GPS.Sate_Num = CombineData_p->gnssInfo.StarNum;
		NAV_Data_Full_p->GPS.delay_pps = (float)CombineData_p->ppsDelay*10.0*(1.0e-9);
		NAV_Data_Full_p->GPS.gpsweek = CombineData_p->gnssInfo.gpsweek;
		//NAV_Data_Full_p->GPS.gpssecond982 = CombineData_p->gnssInfo.gpssecond982;
		//*****新增****pdop**
		NAV_Data_Full_p->GPS.pdop=CombineData_p->factor_pos;
		if((NAV_Data_Full_p->GPS.headingStatus==E_GPS_RTK_FIXED)&&(NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_SPP)&&(NAV_Data_Full_p->GPS.pdop<4.0))//***非RTK的heading***条件要严格一些！！***
		{
			NAV_Data_Full_p->GPS.NO_RTK_heading_flag=RETURN_SUCESS;
		}
		else
		{
			NAV_Data_Full_p->GPS.NO_RTK_heading_flag=RETURN_FAIL;
		}
		//*******新增**********
						//******************heading********************->************
				//if ((NAV_Data_Full_p->GPS.Position_Status != E_GPS_POS_INVALID) && (NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED))//********HEADING 有效******
				//if(NAV_Data_Full_p->KF.measure_flag_head) //*****用到heading更新*******
/* if(NAV_Data_Full_p->GPS.gps_up_flag)//******GNSS有更新则重新赋值******
			{
				if (NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID_RTK)//********RTK的heading****
				{
							if (NAV_Data_Full_p->GPS.baseline < 0.2)//*****转弯是否有影响待定****
							{
								R_heading = HEAD_VAR_OFF;
							}
							else if (NAV_Data_Full_p->GPS.baseline < 4.0)
							{
								R_heading = pow((0.1 + 0.213 * exp(-0.76 * NAV_Data_Full_p->GPS.baseline)) * DEG2RAD, 2);
							}
							else
							{
								R_heading = (0.1 * DEG2RAD) * (0.1 * DEG2RAD);
							}
				}
				else if (NAV_Data_Full_p->GPS.NO_RTK_heading_flag)//*****非RTK的heading*****
				{
							if (NAV_Data_Full_p->GPS.baseline < 0.5)
							{
								R_heading = HEAD_VAR_OFF;
							}
							else if (NAV_Data_Full_p->GPS.baseline < 4.0)
							{
								R_heading = pow((0.3 + 0.188 * exp(-0.66 * NAV_Data_Full_p->GPS.baseline)) * DEG2RAD, 2);
							}
							else
							{
								R_heading = (0.3 * DEG2RAD) * (0.3 * DEG2RAD);
							}
				}
				else
				{
							//NAV_Data_Full_p->KF.measure_flag_head = E_KALMAN_MEASURE_HEADING_NO;//*********舍弃****
							R_heading = HEAD_VAR_OFF;
				}
			}*/
	//****************heading**********************<-************
	
	}
	//gps data check
//	if(RETURN_FAIL == gps_data_check(&NAV_Data_Full))
//	{
//		//SetNavStatus(E_NAV_STATUS_START);		
//	}
}


void GNSS_init()
{
	
}



