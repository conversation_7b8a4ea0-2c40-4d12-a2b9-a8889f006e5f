/***********************************************************************************
kalman filter proccesing
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/

#include "nav_includes.h"
#include <string.h>

#ifndef WIN32
#include "computerFrameParse.h"
extern struct calib_t caliData;
#endif

#ifdef WIN32
	struct timeval currentTime;
	static int time=0;

#endif
int time_delay=0;

static int countgps = 0;

const double TH_pit=(89.5*DEG2RAD);
//****����KF�������ж�****
double bias_acc_max[3] = {0.0,0.0,0.0};
double bias_acc_min[3] = {0.0,0.0,0.0};
double bias_wz_max[3] = { 0.0,0.0,0.0 };
double bias_wz_min[3] = { 0.0,0.0,0.0 };
double b2gps_max = 0.0;
double b2gps_min = 0.0;
//static int KF_Stable_Cnt = 0;
//static int KF_CheckCnt = 0;
//double P_gps_factor = 1 * 1;
//double Q_gps_factor = (0.1) * (0.1) * 0.0;
////double R_wheel_factor = (1.0f)*(1.0f);
//double R_gps_factor = (0.2) * (0.2) ;

//static int quake_count = 0;//�𶯼���
//static int measure_count = 0;
//���򲹳��˲�����
double P_ve_Cvn=(10.0)*(10.0);
double Q_ve_Cvn =(0.001)*(0.001)*0.005;
double R_ve_Cvn =(1.0)*(1.0);
/////////////////////KF_UP//////////////////////////////////
double  P0_odo=(10.0*DEG2RAD)*(10.0*DEG2RAD);
double  P0_b2gps=(10.0*DEG2RAD)*(10.0*DEG2RAD);
//char    flash_flag=1;
/*****************************�������˲������õ��ı�������*******************************************/
//////////////////////////////////////////////////////////////////////////////////////////////////////
//ʱ��������
double XK[NA] = {0};
double FP[NA*NA] = {0};
double FPF[NA*NA] = {0};

double debug_KF_Xk[NA] = {0};
//heading
double R_heading = 0;
double Zk_heading = 0;
double PH_T_heading[NA*1] = {0};
double HPH_T_R_heading = 0;
double HPH_T_R_Inc_heading = 0;
double K_heading[NA*1] = {0};

//vn
double R_vn[3] = {0};
double Zk_vn[3] = {0};
double PH_T_vn[NA*3] = {0};


double HPH_T_R_vn[3*3] = {0};
double HPH_T_R_Inc_vn[3*3] = {0};
double K_vn[NA*3] = {0};

//zupt
#define  ZUPTstd    (0.02)
double R_ZUPT[3] = {ZUPTstd*ZUPTstd,ZUPTstd*ZUPTstd,ZUPTstd*ZUPTstd};
double Zk_ZUPT[3] = {0,0,0};
double PH_T_ZUPT[NA*3] = {0};
double HPH_T_R_ZUPT[3*3] = {0};
double HPH_T_R_Inc_ZUPT[3*3] = {0};
double K_ZUPT[NA*3] = {0};
//zupt heading ����Լ��
double Zk_ZUPT_Heading =0.0;
#define  ZUPTstd_heading    (1.0e-5*DEG2RAD)  //������
const double R_ZUPT_Heading = (ZUPTstd_heading* ZUPTstd_heading);
double ZkPre_ZUPT_heading = 0.0;
double ZUPT_Hk_head[3] = { 0.0,0.0,0.0 };

//pos
double R_pos[3] = {0};
double Zk_pos[3] = {0};
double PH_T_pos[NA*3] = {0};
double HPH_T_R_pos[3*3] = {0};
double HPH_T_R_Inc_pos[3*3] = {0};
double K_pos[NA*3] = {0};

//car
//double Cmn[3*3] = {0};
double Cn2m[3*3] = {0};
double Cm2n[3*3] = {0};
double Ln2m[2*3] = {0};
double Ln2m_T[3*2] = {0};

double Cn2m_vn[3*3] = {0};
double Cn2m_vn_T[3*3] = {0};


double R_m[3] = {0};
double R_m2[3] = { 0 };
double Zk_m[3] = {0};
double PH_T_m[NA*3] = {0};
double HPH_T_R_m[3*3] = {0};
double HPH_T_R_m2[3 * 3] = { 0 };
double HPH_T_R_Inc_m[3*3] = {0};
double K_m[NA*3] = {0}; 
//***********************wheel update**********
double R_m_s= 0;
double Zk_m_s= 0;
double PH_T_m_s[NA*1] = {0};
double HPH_T_R_m_s = 0;
double HPH_T_R_Inc_m_s = 0;
double K_m_s[NA*1] = {0}; 
//**********heading*****
double Hk_head[3]={0.0,0.0,0.0};
//double Hk_head_T[3]={0};



//*************NHC update ********************
double Hk_NHC_Cn2m_vn[2*3]={0};
double Hk_NHC_Cn2m_vn_T[3*2]={0};
double Hk_NHC_Cn2m[2*3]={0};
double Hk_NHC_Cn2m_T[3*2]={0};

double R_m_xz[2] = {0};
double Zk_m_xz[2] = {0};
double PH_T_m_xz[NA*2] = {0};
double HPH_T_R_m_xz[2*2] = {0};
double HPH_T_R_Inc_m_xz[2*2] = {0};
double K_m_xz[NA*2] = {0}; 


//double KHP_[NA*NA] = {0};
double HP_m_xz[2*NA] = {0.0};


//���ٶȼ���̬�����˲���ر���
double att_acc[3] = {0};
float weight = 0;
float weight1 = 0;
double k_filte = 0;
double temp_weight = 0;
double mode_fb = 0;
float fk_adj = 0.005f;
float fk_adj2 = 1;

//int g_KF_UP2_gps_delay_cunt=0;
unsigned char g_valid_wheelspeed=RETURN_SUCESS;
///////////////////////////////////////////////////////////////////////////////////////////////

/*****************************��������*******************************************/

void KF_Init(_NAV_Data_Full_t* NAV_Data_Full_p);//�������˲���ʼ��
//static void LeverarmTimeCorr(double *Zk,_NAV_Data_Full_t* NAV_Data_Full_p,double heading,double* lever,double *gpsVn,double *gpsPos,double dt); //GPS���ݱ۸˲���
static void LeverarmTimeCorr2(double *Zk,_NAV_Data_Full_t* NAV_Data_Full_p,double heading,double* lever,double *gpsVn,double *gpsPos,double dt); //GPS���ݱ۸˲���
static void Init_XPQ(_KF_t* kf, double x,double P, double  Q, int i);//P���Q���ʼ��
//static void Update_Phi(double* Fk,_NAV_Data_Full_t NAV_Data_Full_temp,unsigned char nstep);//KF����Phi����,����һ��״̬���¾���
static void Update_Phi(double* Fk,_NAV_Data_Full_t *NAV_Data_Full_temp,unsigned char nstep);//KF����Phi����,����һ��״̬���¾���
static void KfFeedback(_NAV_Data_Full_t* NAV_Data_Full_p,unsigned char nstep);//Kf״̬����
//unsigned char Update_Phi_2(double* Fk,_NAV_Data_Full_t *pNAV_Data_Full,unsigned char nstep);//KF����Phi����,����һ��״̬���¾���
double CorrHeading(double orginheading);
double CorrHeading_PI(double orginheading);//*****����Ϊrad***
void SetGnssKalmanRmatrix(_NAV_Data_Full_t* NAV_Data_Full_p);
void Update_Phi_HP(double* Fk,_NAV_Data_Full_t *pNAV_Data_Full,unsigned char nstep);

/******************************************************************************/

/*****************************��������*****************************************/

////��������Ƿ�Χ��(-180 180]
//double CorrHeading(double orginheading)
//{
//	if(orginheading<=-180)
//	{
//		return orginheading+360;
//	}
//	else if(orginheading>180)
//	{
//		return orginheading-360;
//	}
//	else
//	{
//		return orginheading;
//	}
//}

void NavStandardParm2Flash(_NAV_Data_Full_t* NAV_Data_Full_p)
{
#ifndef linux
	//�궨��־���û�����
	combineData.Adj.Nav_Standard_flag =NAV_Data_Full_p->Nav_Standard_flag;
	//combineData.Adj.gnssAtt_from_vehicle2[2]=NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2];
//	combineData.Adj.att_ods2_b_filte_2=NAV_Data_Full_p->ODS.att_ods2_b_filte[2];
//	combineData.Adj.att_ods2_b_filte_2_x=NAV_Data_Full_p->ODS.att_ods2_b_filte[0];
	
	combineData.Adj.gnssAtt_from_vehicle2[2]=RAD2DEG*NAV_Data_Full_p->SubKF.att_b2gps[2];
	combineData.Adj.gnssAtt_from_vehicle2[1]=0.0;
	combineData.Adj.gnssAtt_from_vehicle2[0]=0.0;
	combineData.Adj.att_ods2_b_filte_2 =RAD2DEG*NAV_Data_Full_p->SubKF.att_xyz[2];
	combineData.Adj.att_ods2_b_filte_2_x = RAD2DEG*NAV_Data_Full_p->SubKF.att_xyz[0];//***�㷨�ò���**
	//
	combineData.Adj.att_ods2b_filter_deg[0] =RAD2DEG*NAV_Data_Full_p->SubKF.att_xyz[0];
	combineData.Adj.att_ods2b_filter_deg[1] = RAD2DEG*NAV_Data_Full_p->SubKF.att_xyz[1];//*********roll���޲ο�����*****
	combineData.Adj.att_ods2b_filter_deg[2] =RAD2DEG*NAV_Data_Full_p->SubKF.att_xyz[2];
	for(int j =0;j<3;j++)
	{
		NAV_Data_Full_p->Param.gyro_off[j] =NAV_Data_Full_p->SINS.eb[j] ;//+ NAV_Data_Full_p->Param.gyro_off[j];
		NAV_Data_Full_p->Param.acc_off[j] =NAV_Data_Full_p->SINS.db[j] ;//+ NAV_Data_Full_p->Param.acc_off[j];
		//NAV_Data_Full_p->SINS.eb[j] = 0;
		//NAV_Data_Full_p->SINS.db[j] = 0;
		combineData.Adj.gyro_off[j] =  NAV_Data_Full_p->Param.gyro_off[j];
		combineData.Adj.acc_off[j] =  NAV_Data_Full_p->Param.acc_off[j];
	}
	memcpy(&caliData.adj,&combineData.Adj,sizeof(AdjPara_t));
    comm_saveCaliData();
#endif
}
/******************************************************************************
*ԭ  �ͣ�void Q_MAT_UP(_NAV_Data_Full_t* NAV_Data_Full_p)
*��  �ܣ�����Q����
*��  �룺
*��  ������
*******************************************************************************/
void Q_MAT_UP(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	double Q_tempt[9] = { 0 };
	double ts_KF = ((double)NAV_Data_Full_p->SINS.nts * KLMAN_FILTER_SETP_SUM);
	int i;
	double* Q_mat = eyes(3);
	double tau = 1000;
	if (E_IMU_MANU_460 == NAV_Data_Full_p->memsType)
	{
		Q_mat[0] = IMU460_ANGNRANDOMWALK2 / 25 * ts_KF;
		Q_mat[4] = IMU460_ANGNRANDOMWALK2 / 25 * ts_KF;
		Q_mat[8] = IMU460_ANGNRANDOMWALK2 / 25 * ts_KF;
		//NAV_Data_Full_p->KF.Qk[9 + NA * 9] = IMU460_GYROBIASSTABILITY2 * 2 / tau;
		//NAV_Data_Full_p->KF.Qk[10 + NA * 10] = IMU460_GYROBIASSTABILITY2 * 2 / tau;
		//NAV_Data_Full_p->KF.Qk[11 + NA * 11] = IMU460_GYROBIASSTABILITY2 * 2 / tau;
		//NAV_Data_Full_p->KF.Qk[12 + NA * 12] = IMU460_ACCBIASSTABILITY2 * 2 / tau;
		//NAV_Data_Full_p->KF.Qk[13 + NA * 13] = IMU460_ACCBIASSTABILITY2 * 2 / tau;
		//NAV_Data_Full_p->KF.Qk[14 + NA * 14] = IMU460_ACCBIASSTABILITY2 * 2 / tau;
		/*NAV_Data_Full_p->KF.Qk[9 + NA * 9] = SCHA634_GYROBIASSTABILITY2 * 2 / tau * ts_KF;
		NAV_Data_Full_p->KF.Qk[10 + NA * 10] = SCHA634_GYROBIASSTABILITY2 * 2 / tau * ts_KF;
		NAV_Data_Full_p->KF.Qk[11 + NA * 11] = SCHA634_GYROBIASSTABILITY2 * 2 / tau * ts_KF;*/
	}
	else if (E_IMU_MANU_SCHA63X == NAV_Data_Full_p->memsType)
	{
		Q_mat[0] = SCHA634_ANGNRANDOMWALK2 / 25 * ts_KF;
		Q_mat[4] = SCHA634_ANGNRANDOMWALK2 / 25 * ts_KF;
		Q_mat[8] = SCHA634_ANGNRANDOMWALK2 / 25 * ts_KF;
		/*NAV_Data_Full_p->KF.Qk[9 + NA * 9] = SCHA634_GYROBIASSTABILITY2 * 2 / tau * ts_KF;
		NAV_Data_Full_p->KF.Qk[10 + NA * 10] = SCHA634_GYROBIASSTABILITY2 * 2 / tau * ts_KF;
		NAV_Data_Full_p->KF.Qk[11 + NA * 11] = SCHA634_GYROBIASSTABILITY2 * 2 / tau * ts_KF;
		NAV_Data_Full_p->KF.Qk[12 + NA * 12] = SCHA634_ACCBIASSTABILITY2 * 2 / tau * ts_KF;
		NAV_Data_Full_p->KF.Qk[13 + NA * 13] = SCHA634_ACCBIASSTABILITY2 * 2 / tau * ts_KF;
		NAV_Data_Full_p->KF.Qk[14 + NA * 14] = SCHA634_ACCBIASSTABILITY2 * 2 / tau * ts_KF;*/
	}
	if (E_IMU_ATT_IFOG == NAV_Data_Full_p->imuSelect)
	{
		Q_mat[8] = FOG_ANGNRANDOMWALK2 / 25 * ts_KF;
	}
	//matmul("NN", 3, 3, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, Q_mat, 0.0, Q_tempt);//GQG'
	//matmul("NT", 3, 3, 3, 1.0, Q_tempt, NAV_Data_Full_p->SINS.Cb2n, 0.0, Q_mat);
	//for (i = 0; i < 3; i++)
	//{
	//	NAV_Data_Full_p->KF.Qk[i + NA * i] = fabs(Q_mat[i+3*i]);
	//}
	NAV_Data_Full_p->KF.Qk[0] = Q_mat[0];
	NAV_Data_Full_p->KF.Qk[16] = Q_mat[4];
	NAV_Data_Full_p->KF.Qk[32] = Q_mat[8];

}
/******************************************************************************
*ԭ  �ͣ�void KF_Init(KF_t *kf)
*��  �ܣ���ʼ���������˲���
*��  �룺 
*��  ������
*******************************************************************************/
void KF_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	double KF_nts;
	int i = 0;
	int j = 0;
	double tau = 1000.0;
#ifdef WIN32
	inav_log(INAVMD(LOG_DEBUG),"KF_Init");
#endif
	KF_nts = ((double)NAV_Data_Full_p->SINS.nts*KLMAN_FILTER_SETP_SUM);//*(NAV_Data_Full_p->SINS.nts*KLMAN_FILTER_SETP_SUM);
	for(i = 0;i<NA;i++)
	{
		for(j = 0;j<NA;j++)
		{
			NAV_Data_Full_p->KF.Pxk[i+NA*j] = 0.0;
		}
		NAV_Data_Full_p->KF.Xk[i] = 0.0;
	}
    //XPQ
	if(E_IMU_MANU_460 == NAV_Data_Full_p->memsType)//��׼IMU460
	{
		//d_att	
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT_VAR, IMU460_ANGNRANDOMWALK2 * KF_nts, 0); //att   ANGNRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT_VAR, IMU460_ANGNRANDOMWALK2 * KF_nts, 1); //att   ANGNRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT2_VAR, IMU460_ANGNRANDOMWALK2 * KF_nts, 2); //att   ANGNRANDOMWALK2*nts*nts

		//dv
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DVEL_VAR, IMU460_VELRANDOMWALK2 * KF_nts, 3); //vel   VELRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DVEL_VAR, IMU460_VELRANDOMWALK2 * KF_nts, 4); //vel   VELRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DVEL_VAR, IMU460_VELRANDOMWALK2 * KF_nts, 5); //  20 * UGPSHZ * 20 * UGPSHZ*nts*nts
		//dp
//			Init_XPQ(kf, 0.0, DPOS_VAR*100, INS_POS_VAR*KF_nts, i); 
//			Init_XPQ(kf, 0.0, 1 * 1*100, INS_HEAD_VAR*KF_nts, i);	
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DPOS_VAR, 0.0, 6); //pos
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DPOS_VAR, 0.0, 7); //pos	
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DPOS2_VAR, 0.0, 8);          //ע��˴�����1 * 1,���ڸ߳�ʹ���׵�λ������Ҫ����Re
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DPOS_VAR2, 0.0, 6); //pos
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DPOS_VAR2, 0.0, 7); //pos	
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, 1 * 1, 0.0, 8);          //ע��˴�����1 * 1,���ڸ߳�ʹ���׵�λ������Ҫ����Re

	    //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, IMU460_GYROBIAS2, IMU460_GYROBIASSTABILITY2 * 2 / tau * KF_nts, 9);  //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, IMU460_GYROBIAS2, IMU460_GYROBIASSTABILITY2 * 2 / tau * KF_nts, 10); //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, IMU460_GYROBIAS2, IMU460_GYROBIASSTABILITY2 * 2 / tau * KF_nts, 11); //eb

		//db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, IMU460_ACCBIAS2, IMU460_ACCBIASSTABILITY2 * 2 / tau * KF_nts, 12); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, IMU460_ACCBIAS2, IMU460_ACCBIASSTABILITY2 * 2 / tau * KF_nts, 13); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, IMU460_ACCBIAS2, IMU460_ACCBIASSTABILITY2 * 2 / tau * KF_nts, 14); //db
		//es
		if(NA>15)
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, IMU460_GYROSCALE2, 0.0, 15); //es��������
		//dlever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 15); //lever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 16); //lever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 17); //lever	
		
		if(E_IMU_ATT_MEMS==NAV_Data_Full_p->imuSelect)//NAV_USE_MEMS
		{
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT2_VAR, IMU460_ANGNRANDOMWALK2 * KF_nts, 2);//d_att
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, IMU460_GYROBIAS2, 0.0, 11); //eb
		}
		else if(E_IMU_ATT_IFOG==NAV_Data_Full_p->imuSelect )//NAV_USE_FOG+MEMS
		{
			
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT2_VAR, FOG_ANGNRANDOMWALK2 * KF_nts, 2);//d_att
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, FOG_GYROBIAS2, 0.0, 11); //eb
		}
	}
	else if(E_IMU_MANU_SCHA63X == NAV_Data_Full_p->memsType)//����634
	{
				//d_att	
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DATT_VAR, SCHA634_ANGNRANDOMWALK2 * KF_nts*6, 0); //att   ANGNRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DATT_VAR, SCHA634_ANGNRANDOMWALK2 * KF_nts*6, 1); //att   ANGNRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DATT2_VAR, SCHA634_ANGNRANDOMWALK2 * KF_nts*6, 2); //att   ANGNRANDOMWALK2*nts*nts

		//dv
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DVEL_VAR, SCHA634_VELRANDOMWALK2 * KF_nts*6, 3); //vel   VELRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DVEL_VAR, SCHA634_VELRANDOMWALK2 * KF_nts*6, 4); //vel   VELRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DVEL_VAR, SCHA634_VELRANDOMWALK2 * KF_nts*6, 5); //  20 * UGPSHZ * 20 * UGPSHZ*nts*nts
		//dp
//			Init_XPQ(kf, 0.0, DPOS_VAR*100, INS_POS_VAR*KF_nts, i); 
//			Init_XPQ(kf, 0.0, 1 * 1*100, INS_HEAD_VAR*KF_nts, i);	
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, 1*DPOS_VAR, 0.0, 6); //pos
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, 1*DPOS_VAR, 0.0, 7); //pos	
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, 4.0, 0.0, 8);          //ע��˴�����1 * 1,���ڸ߳�ʹ���׵�λ������Ҫ����Re
		//static void Init_XPQ(_KF_t* kf, double x,double P, double  Q, int i)//P���Q���ʼ��
		/*
		double  NEW_DPOS_VAR_LL, NEW_DPOS_VAR_H ;
		if (NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID_RTK)
		{
			NEW_DPOS_VAR_LL = DPOS_VAR / 900.0;//*****0.03m******
			NEW_DPOS_VAR_H = DPOS2_VAR / 400.0;//****0.05m****
		}
		else//(NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID)
		{
			NEW_DPOS_VAR_LL = DPOS_VAR*9.0;//*****3m******
			NEW_DPOS_VAR_H = DPOS2_VAR*25.0;//****5m****
		}
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, NEW_DPOS_VAR_LL, 0.0, 6); //pos
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, NEW_DPOS_VAR_LL, 0.0, 7); //pos	
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, NEW_DPOS_VAR_H, 0.0, 8);          //ע��˴�����1 * 1,���ڸ߳�ʹ���׵�λ������Ҫ����Re
		*/
	    //eb
		/*
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_GYROBIAS2, SCHA634_GYROBIASSTABILITY2 * 2 / tau * KF_nts, 9); //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_GYROBIAS2, SCHA634_GYROBIASSTABILITY2 * 2 / tau * KF_nts, 10); //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_GYROBIAS2, SCHA634_GYROBIASSTABILITY2 * 2 / tau * KF_nts, 11); //eb
		*/
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_GYROBIAS2, SCHA634_GYROBIASSTABILITY2 * KF_nts, 9); //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_GYROBIAS2, SCHA634_GYROBIASSTABILITY2 * KF_nts, 10); //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_GYROBIAS2, SCHA634_GYROBIASSTABILITY2 * KF_nts, 11); //eb
		//db
		/*
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_ACCBIAS2, SCHA634_ACCBIASSTABILITY2 * 2 / tau * KF_nts, 12); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_ACCBIAS2, SCHA634_ACCBIASSTABILITY2 * 2 / tau * KF_nts, 13); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_ACCBIAS2*1, SCHA634_ACCBIASSTABILITY2 * 2 / tau * KF_nts, 14); //db
		*/
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_ACCBIAS2, SCHA634_ACCBIASSTABILITY2 * KF_nts, 12); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_ACCBIAS2, SCHA634_ACCBIASSTABILITY2 * KF_nts, 13); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_ACCBIAS2*1, SCHA634_ACCBIASSTABILITY2 * KF_nts, 14); //db
		//es
		if(NA>15)
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_GYROSCALE2, 0.0, 15); //es��������
		//dlever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 15); //lever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 16); //lever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 17); //lever	
		
		if(E_IMU_ATT_MEMS==NAV_Data_Full_p->imuSelect)//NAV_USE_MEMS
		{
			//Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT2_VAR, SCHA634_ANGNRANDOMWALK2 * KF_nts, 2);//d_att
			//Init_XPQ(&NAV_Data_Full_p->KF, 0.0, SCHA634_GYROBIAS2, 0.0, 11); //eb
		}
		else if(E_IMU_ATT_IFOG==NAV_Data_Full_p->imuSelect)//NAV_USE_FOG
		{
			
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT2_VAR, FOG_ANGNRANDOMWALK2 * KF_nts, 2);//d_att
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, FOG_GYROBIAS2, 0.0, 11); //eb
		}
	}
	else if(E_IMU_MANU_ADIS16465 == NAV_Data_Full_p->memsType)//ADIS16465
	{
				//d_att	
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DATT_VAR, ADIS16465_ANGNRANDOMWALK2 * KF_nts, 0); //att   ANGNRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DATT_VAR, ADIS16465_ANGNRANDOMWALK2 * KF_nts, 1); //att   ANGNRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DATT2_VAR, ADIS16465_ANGNRANDOMWALK2 * KF_nts, 2); //att   ANGNRANDOMWALK2*nts*nts

		//dv
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DVEL_VAR, ADIS16465_VELRANDOMWALK2 * KF_nts, 3); //vel   VELRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DVEL_VAR, ADIS16465_VELRANDOMWALK2 * KF_nts, 4); //vel   VELRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DVEL_VAR, ADIS16465_VELRANDOMWALK2 * KF_nts, 5); //  20 * UGPSHZ * 20 * UGPSHZ*nts*nts

		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DPOS_VAR, 0.0, 6); //pos
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DPOS_VAR, 0.0, 7); //pos	
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, 1 * 1, 0.0, 8);          //ע��˴�����1 * 1,���ڸ߳�ʹ���׵�λ������Ҫ����Re
	    //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, ADIS16465_GYROBIAS2, 0.0, 9); //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, ADIS16465_GYROBIAS2, 0.0, 10); //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, ADIS16465_GYROBIAS2, 0.0, 11); //eb

		//db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, ADIS16465_ACCBIAS2, 0.0, 12); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, ADIS16465_ACCBIAS2, 0.0, 13); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, ADIS16465_ACCBIAS2, 0.0, 14); //db
		//dlever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 15); //lever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 16); //lever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 17); //lever	
		
		if(E_IMU_ATT_MEMS==NAV_Data_Full_p->imuSelect)//NAV_USE_MEMS
		{
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT2_VAR, ADIS16465_ANGNRANDOMWALK2 * KF_nts, 2);//d_att
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, ADIS16465_GYROBIAS2, 0.0, 11); //eb
		}
		else if(E_IMU_ATT_IFOG==NAV_Data_Full_p->imuSelect)//NAV_USE_FOG
		{
			
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT2_VAR, FOG_ANGNRANDOMWALK2 * KF_nts, 2);//d_att
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, FOG_GYROBIAS2, 0.0, 11); //eb
		}
	}
	else if(E_IMU_MANU_EPSON_G370 == NAV_Data_Full_p->memsType)//ADIS16465
	{
				//d_att	
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DATT_VAR, EPSON_G370_ANGNRANDOMWALK2 * KF_nts, 0); //att   ANGNRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DATT_VAR, EPSON_G370_ANGNRANDOMWALK2 * KF_nts, 1); //att   ANGNRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DATT2_VAR, EPSON_G370_ANGNRANDOMWALK2 * KF_nts, 2); //att   ANGNRANDOMWALK2*nts*nts

		//dv
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DVEL_VAR, EPSON_G370_VELRANDOMWALK2 * KF_nts, 3); //vel   VELRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DVEL_VAR, EPSON_G370_VELRANDOMWALK2 * KF_nts, 4); //vel   VELRANDOMWALK2*nts*nts
		Init_XPQ(&NAV_Data_Full_p->KF, 0, DVEL_VAR, EPSON_G370_VELRANDOMWALK2 * KF_nts, 5); //  20 * UGPSHZ * 20 * UGPSHZ*nts*nts

		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DPOS_VAR, 0.0, 6); //pos
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DPOS_VAR, 0.0, 7); //pos	
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, 1 * 1, 0.0, 8);          //ע��˴�����1 * 1,���ڸ߳�ʹ���׵�λ������Ҫ����Re
	    //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, EPSON_G370_GYROBIAS2, 0.0, 9); //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, EPSON_G370_GYROBIAS2, 0.0, 10); //eb
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, EPSON_G370_GYROBIAS2, 0.0, 11); //eb

		//db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, EPSON_G370_ACCBIAS2, 0.0, 12); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, EPSON_G370_ACCBIAS2, 0.0, 13); //db
		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, EPSON_G370_ACCBIAS2, 0.0, 14); //db
		//dlever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 15); //lever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 16); //lever
//		Init_XPQ(&NAV_Data_Full_p->KF, 0.0, LEVER_VAR, 0.0, 17); //lever	
		
		if(E_IMU_ATT_MEMS==NAV_Data_Full_p->imuSelect)//NAV_USE_MEMS
		{
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT2_VAR, EPSON_G370_ANGNRANDOMWALK2 * KF_nts, 2);//d_att
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, EPSON_G370_GYROBIAS2, 0.0, 11); //eb
		}
		else if(E_IMU_ATT_IFOG==NAV_Data_Full_p->imuSelect)//NAV_USE_FOG
		{
			
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, DATT2_VAR, FOG_ANGNRANDOMWALK2 * KF_nts, 2);//d_att
			Init_XPQ(&NAV_Data_Full_p->KF, 0.0, FOG_GYROBIAS2, 0.0, 11); //eb
		}
	}
	#if 0
	//************�Ȳ�����P�����������
    //P_min P_max
	{
		for (i = 0; i < NA; i++)
		{
			NAV_Data_Full_p->KF.P_min[i] = NAV_Data_Full_p->KF.Pxk[i+i*NA] * 1.0e-6;//1.0e-6;//1.0e-4;
			NAV_Data_Full_p->KF.P_max[i] = (NAV_Data_Full_p->KF.Pxk[i+i*NA]) * 1.0e10;
		}
		NAV_Data_Full_p->KF.P_min[3] = 1.0e-3;
		NAV_Data_Full_p->KF.P_min[4] = 1.0e-3;
		NAV_Data_Full_p->KF.P_min[5] = 1.0e-3;
		//Pk
		for (i = 0; i < NA; i++)
		{		
			for(j = 0;j<NA;j++)
			{
				NAV_Data_Full_p->KF.Pxk[i+j*NA]  = NAV_Data_Full_p->KF.Pxk[i+j*NA]* 1;			
			}	
		}
	}
 #endif
//״̬ת�ƾ����ʼ��
#if 0
	for (i = 0; i < NA; i++)
	{
		for (j = 0; j < NA; j++)
		{
			if(i==j)
			{
				NAV_Data_Full_p->KF.Fk[i + i * NA] = 1.0;
			}
			else
			{
				NAV_Data_Full_p->KF.Fk[j + i * NA] = 0.0;
			}
		}
	}
#endif	
	EyeMatrix(NAV_Data_Full_p->KF.Fk, NA, 1.0);

	//��ǰ״̬��ʼ����ɣ���δ��ʼkalman�˲�
	NAV_Data_Full_p->KF.step = E_KALMAN_FILTER_NONE;
//	NAV_Data_Full_p->KF.step =E_KALMAN_TIME_UPDATE_1;
	//***************subKF initial*******************************************
	for(i=0;i<3;i++)
	{
		NAV_Data_Full_p->SubKF.Xk[i]=0;
	}
	//**P**
	for(i=0;i<3;i++)
	{
	 for(j=0;j<3;j++)
		{
			if(i==j)
				NAV_Data_Full_p->SubKF.Pk[i+3*j]=P0_odo;
			else
				NAV_Data_Full_p->SubKF.Pk[i+3*j]=0;
		}
	}
	//******b2v************
			double angle_tmp[3]={0.0, 0.0, 0.0}; //**rad**
			if(NAV_Data_Full_p->Nav_Standard_flag == E_NAV_STANDARD_PROCCSSED)
			{
				angle_tmp[0]=NAV_Data_Full_p->ODS.att_ods2_b_filte[0]*DEG2RAD;
				angle_tmp[1]=NAV_Data_Full_p->ODS.att_ods2_b_filte[1]*DEG2RAD;
				angle_tmp[2]=NAV_Data_Full_p->ODS.att_ods2_b_filte[2]*DEG2RAD;
			}
	   att2qnb(angle_tmp, NAV_Data_Full_p->SubKF.Q_b2m); //*******ok!**
	   Qnb2Cnb(NAV_Data_Full_p->SubKF.Q_b2m, NAV_Data_Full_p->SubKF.Cmb);
	//****initial***b2gps****
	  NAV_Data_Full_p->SubKF.att_b2gps[2]=0;
	  NAV_Data_Full_p->SubKF.att_b2gps[1]=0;
	  NAV_Data_Full_p->SubKF.att_b2gps[0]=0;
			if(NAV_Data_Full_p->Nav_Standard_flag == E_NAV_STANDARD_PROCCSSED)
			{
	  NAV_Data_Full_p->SubKF.att_b2gps[2]=NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2]*DEG2RAD;
	  NAV_Data_Full_p->SubKF.att_b2gps[1]=NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[1]*DEG2RAD;//*****ע�ⵥλҪһ�£���***
	  NAV_Data_Full_p->SubKF.att_b2gps[0]=NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[0]*DEG2RAD;
			}
	  NAV_Data_Full_p->SubKF.P_b2gps=P0_b2gps;
			//
			//****Guolong Zhang**20240720**********->****
	  NAV_Data_Full_p->ODS.scale_factor_filte=1.0;
	  NAV_Data_Full_p->ODS.P_wheel_fact =0.05*0.05*9;
//****Guolong Zhang**20240720************<-****
	//***************subKF initial**********************************************
}

void SetGnssKalmanRmatrix(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	//******20240727*******Guolong Zhang********->*****
		if(NAV_Data_Full_p->GPS.Position_Status != E_GPS_POS_INVALID)
		{
			 if(NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED)
				{
						R_vn[0] 	= VEL_VAR2;//0.05*0.05;
						R_vn[1] 	= VEL_VAR2;//0.05*0.05;
						R_vn[2] 	= VEL_VAR2;//0.05*0.05;
						R_pos[0] 	= RTK_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
						R_pos[1] 	= RTK_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
						R_pos[2] 	= RTK_POS_HEIGHT_VAR2;//0.05*0.05;	
						//*********
						R_heading = (0.2 * DEG2RAD) * (0.2 * DEG2RAD);
				}
				else if(NAV_Data_Full_p->GPS.rtkStatus ==E_GPS_RTK_SPP)//********���㶨λ���****
				{
					 R_vn[0] 	= VEL_VAR2 * 1;
						R_vn[1] 	= VEL_VAR2 * 1;//*****������Ϊһ��*****
						R_vn[2] 	= VEL_VAR2 * 1;
						//************
						R_heading = (0.4 * DEG2RAD) * (0.4 * DEG2RAD);
					 if(NAV_Data_Full_p->GPS.pdop>10.0)
						{
								NAV_Data_Full_p->KF.measure_flag_vn=E_KALMAN_MEASURE_VEL_NO;//*****������****
							 NAV_Data_Full_p->KF.measure_flag_pos=E_KALMAN_MEASURE_POS_NO;
						}
						else if(NAV_Data_Full_p->GPS.pdop>4.0)
						{
							 R_pos[0] 	= NAV_Data_Full_p->GPS.pdop*SPP_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
								R_pos[1] 	= NAV_Data_Full_p->GPS.pdop*SPP_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
								R_pos[2] 	= NAV_Data_Full_p->GPS.pdop*SPP_POS_HEIGHT_VAR2;
						}
						else
						{
								R_pos[0] 	= SPP_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
								R_pos[1] 	= SPP_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
								R_pos[2] 	= SPP_POS_HEIGHT_VAR2;
						}
				}
				else//*****������λ������Ҳ�����********
			     {
										NAV_Data_Full_p->KF.measure_flag_vn = E_KALMAN_MEASURE_VEL_NO;//*****������****
										NAV_Data_Full_p->KF.measure_flag_pos = E_KALMAN_MEASURE_POS_NO;
										NAV_Data_Full_p->KF.measure_flag_head = E_KALMAN_MEASURE_HEADING_NO;
			     }
		}
	//*******heading************->******
		#if 0
			//******20240828******Guolong Zhang************
			//if ((NAV_Data_Full_p->GPS.Position_Status != E_GPS_POS_INVALID) && (NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED))//********HEADING ��Ч******
				if(NAV_Data_Full_p->KF.measure_flag_head) //*****�õ�heading����*******
			{
				if (NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID_RTK)//********RTK��heading****
				{
					if (NAV_Data_Full_p->GPS.baseline < 0.2)//*****ת���Ƿ���Ӱ�����****
					{
						R_heading = HEAD_VAR_OFF;
					}
					else if (NAV_Data_Full_p->GPS.baseline < 4.0)
					{
						R_heading = pow((0.15 + 0.213 * exp(-0.76 * NAV_Data_Full_p->GPS.baseline)) * DEG2RAD, 2);
					}
					else
					{
						R_heading = (0.15 * DEG2RAD) * (0.15 * DEG2RAD);
					}
				}
				else if (NAV_Data_Full_p->GPS.NO_RTK_heading_flag)//*****��RTK��heading*****
				{
					if (NAV_Data_Full_p->GPS.baseline < 0.5)
					{
						R_heading = HEAD_VAR_OFF;
					}
					else if (NAV_Data_Full_p->GPS.baseline < 4.0)
					{
						R_heading = pow((0.5 + 0.188 * exp(-0.66 * NAV_Data_Full_p->GPS.baseline)) * DEG2RAD, 2);
					}
					else
					{
						R_heading = (0.5 * DEG2RAD) * (0.5 * DEG2RAD);
					}
				}
				else
				{
					NAV_Data_Full_p->KF.measure_flag_head = E_KALMAN_MEASURE_HEADING_NO;//*********����****
				}

//******��ʱ�ù̶�ֵ******
			}
		#endif
	//********heading*************<-*****
	#if 0
	
	if(NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED)
	{
		/*
		//��ʾû��ת��ʱ��
		if((fabs(NAV_Data_Full.IMU.gyro_use[2]*RAD2DEG)<2.0f))//2.0f
		{
			R_heading 	= RTK_HEAD_VAR;
		}
		else//ת�亽������ϴ�
		{
			R_heading 	= RTK_TURN_HEAD_VAR;
		}
		*/
		//*******�Ȳ������Ƿ�ת�����***
		if(NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED)
		{
			R_heading 	= RTK_HEAD_VAR;
		}

		R_vn[0] 	= VEL_VAR2;//0.05*0.05;
		R_vn[1] 	= VEL_VAR2;//0.05*0.05;
		R_vn[2] 	= VEL_VAR2;//0.05*0.05;
		R_pos[0] 	= RTK_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
	 R_pos[1] 	= RTK_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
		R_pos[2] 	= RTK_POS_HEIGHT_VAR2;//0.05*0.05;	
	}
	else if(NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_SPP)
	{	
		/*
		countgps ++;
		if (countgps >20)
		{
			R_heading 	= HEAD_VAR_OFF;
			R_vn[0] 	= VEL_VAR2 * 5*5;
			R_vn[1] 	= VEL_VAR2 * 5*5;
			R_vn[2] 	= VEL_VAR2 * 5*5;
			R_pos[0] 	= SPP_POS_LON_LAT_VAR2*10*10;//SPP_POS_VAR2;
			R_pos[1] 	= SPP_POS_LON_LAT_VAR2*10*10;//SPP_POS_VAR2;
			R_pos[2] 	= SPP_POS_HEIGHT_VAR2*10*10;
		}
		else
		{	R_heading 	= HEAD_VAR_OFF;
			R_vn[0] 	= VEL_VAR2 * 5*5;
			R_vn[1] 	= VEL_VAR2 * 5*5;
			R_vn[2] 	= VEL_VAR2 * 5*5;
			R_pos[0] 	= SPP_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
			R_pos[1] 	= SPP_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
			R_pos[2] 	= SPP_POS_HEIGHT_VAR2;
			}
		*/
		//*******SPP״̬heading�޷����ã�******
			R_vn[0] 	= VEL_VAR2 * 4*4;
			R_vn[1] 	= VEL_VAR2 * 4*4;//******
			R_vn[2] 	= VEL_VAR2 * 4*4;
			R_pos[0] 	= SPP_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
			R_pos[1] 	= SPP_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
			R_pos[2] 	= SPP_POS_HEIGHT_VAR2;
			
  if(NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED)  //*****���������ӣ�����*****
		{
			R_heading 	= NO_RTK_HEAD_VAR;
		}
			
	}
	else if(NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_DGPS)
	{
		//R_heading 	= HEAD_VAR_OFF;//******
		R_vn[0] 	= VEL_VAR2* 4*4;
		R_vn[1] 	= VEL_VAR2* 4*4;
		R_vn[2] 	= VEL_VAR2* 4*4;
		R_pos[0]	= DGPS_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
		R_pos[1]	= DGPS_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
		R_pos[2]	= DGPS_POS_HEIGHT_VAR2;
	 if(NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED)
		{
			R_heading 	= NO_RTK_HEAD_VAR;//��ʱʹ��ͬһ��ֵ
		}
		
	}

	
	#endif
}

float CalGPSTimeCompensate(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	return 0.0;
}

//double g_wheel_vel=0.0;
//double P_wheel_vel=1000.0*1000.0;
//double Q_wheel_vel=0.1*0.1;//0.02*0.02
//double R_wheel_vel=2.0*2.0;


//����GPS������µĹ۲�����R��������
void GPSObsCalAndNoiseSet(_NAV_Data_Full_t* NAV_Data_Full_p)
{ 	
	double GnssPosition[3] = {0};
	double  GnssVel[3] = {0};
	double  Gnss_heading = 0;
	double  lever[3] = {0};
 double  timdly=0.0;
	//�۲�����ֵ
	{	
		//GPS��λ��
		GnssPosition[0]	=	NAV_Data_Full_p->GPS.Lat * DEG2RAD;//��λrad
		GnssPosition[1]	=	NAV_Data_Full_p->GPS.Lon * DEG2RAD;//��λrad
		GnssPosition[2]	=	NAV_Data_Full_p->GPS.Altitude;//�̵߳�λm 	
		GnssVel[0] 		= 	NAV_Data_Full_p->GPS.ve; 
		GnssVel[1] 		= 	NAV_Data_Full_p->GPS.vn; 
		GnssVel[2] 		= 	NAV_Data_Full_p->GPS.vu; 
		//�˱�ʸ��,��λm
		lever[0] 		= 	NAV_Data_Full_p->Param.gnssArmLength[0]; 
		lever[1] 		= 	NAV_Data_Full_p->Param.gnssArmLength[1]; 
		lever[2] 		= 	NAV_Data_Full_p->Param.gnssArmLength[2]; 
		timdly=NAV_Data_Full_p->GPS.delay_pps;
		//GPS���ݱ۸˲����Լ�ʱ��ͬ������
#if 0
		{
			//����GPSʱ�䲹��
			/*
			{	unsigned int gpssecond = 0;
				gpssecond = NAV_Data_Full_p->GPS.gpssecond;
				while (gpssecond < NAV_Data_Full_p->GPS.gpssecond982)
				{
					gpssecond = gpssecond + 200;
				}
				NAV_Data_Full_p->GPS.delay_pps = (gpssecond-NAV_Data_Full_p->GPS.gpssecond982)/1000.0f;
				//NAV_Data_Full_p->GPS.delay = NAV_Data_Full_p->GPS.delay_pps + g_KF_UP2_gps_delay_cunt*NAV_Data_Full_p->SINS.ts;  //0.005s
				//NAV_Data_Full_p->GPS.delay_pps = (NAV_Data_Full_p->GPS.gpssecond-NAV_Data_Full_p->GPS.gpssecond982)/1000.0;

				NAV_Data_Full_p->GPS.delay = NAV_Data_Full_p->GPS.delay_pps + (g_KF_UP2_gps_delay_cunt-1.0)*NAV_Data_Full_p->SINS.ts-0.01;  //0.005s
				g_KF_UP2_gps_delay_cunt = 0;
				//NAV_Data_Full_p->GPS.delay = CalGPSTimeCompensate(NAV_Data_Full_p);
			}
			//
			*/
		
			
			
#if 0
			//�۲⺽���
			if(E_NAV_STATUS_IN_NAV == NAV_Data_Full_p->Nav_Status)
			{
				Gnss_heading = CorrHeading(-NAV_Data_Full_p->GPS.Heading
								 + NAV_Data_Full_p->Param.gnssAtt_from_vehicle[2]
								 - NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2]
				                ) * DEG2RAD;
				
				LeverarmTimeCorr2( NAV_Data_Full_p->KF.Zk,
							  NAV_Data_Full_p,
							  Gnss_heading,
							  lever,
							  GnssVel,
							  GnssPosition,
							  NAV_Data_Full_p->GPS.delay);	
				
#if 0						
				if(fabs(NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG)<2.0f)
				{
					NAV_Data_Full_p->KF.RTK_Heading_OK = 1;
				}
#endif						
			}
			else if(E_NAV_STATUS_SYSTEM_STANDARD ==  NAV_Data_Full_p->Nav_Status)
			{
				Gnss_heading = CorrHeading(-NAV_Data_Full_p->GPS.Heading
								+ NAV_Data_Full_p->Param.gnssAtt_from_vehicle[2]
								) * DEG2RAD;
				//�����ٶȡ�λ�òв���� 
				LeverarmTimeCorr2( NAV_Data_Full_p->KF.Zk,
								  NAV_Data_Full_p,
								  Gnss_heading,
								  lever,
								  GnssVel,
								  GnssPosition,
								  NAV_Data_Full_p->GPS.delay);
				/*
				//��ʾû��ת��ʱ��
				if(		(fabs(NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG)<MIN_TURN_GYRO_VALUE)//2.0f
					//&&	(RETURN_SUCESS==is_gnss_update(NAV_Data_Full_p))
					&&	(NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED)
				   )
				{
					//Scalar_KalmanFilte(&NAV_Data_Full.Param.gnssAtt_from_vehicle2[2],NAV_Data_Full_p->KF.Zk[6]*RAD2DEG,&P_ve_Cvn,Q_ve_Cvn,R_ve_Cvn);
					//Zk����̬�仯???�Ƿ������˲���
					//NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2] = NAV_Data_Full_p->KF.Zk[6]*RAD2DEG;
					//NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2] =0;
					if(sqrt(NAV_Data_Full_p->KF.Pxk[2 + NA * 2]) < 0.4 * DEG2RAD)
					Scalar_KalmanFilte(&NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2], NAV_Data_Full_p->KF.Zk[6] * RAD2DEG, &P_gps_factor, Q_gps_factor, R_gps_factor, 0.1);
				}
				*/
			}
#endif
			//�����ٶȡ�λ�òв���� 
//***************20240718*****Guolong Zhang*****->*****
  Gnss_heading=NAV_Data_Full_p->GPS.Heading_cor*DEG2RAD;
		LeverarmTimeCorr2( NAV_Data_Full_p->KF.Zk,
																					NAV_Data_Full_p,
																					Gnss_heading,
																					lever,
																					GnssVel,
																					GnssPosition,
																					timdly);
//***************20240718*****Guolong Zhang*****<-*****
		}
#endif
		//�����ٶȡ�λ�òв���� 
//***************20240718*****Guolong Zhang*****->*****
		Gnss_heading = NAV_Data_Full_p->GPS.Heading_cor * DEG2RAD;
		LeverarmTimeCorr2(NAV_Data_Full_p->KF.Zk,
			NAV_Data_Full_p,
			Gnss_heading,
			lever,
			GnssVel,
			GnssPosition,
			timdly);
		//***************20240718*****Guolong Zhang*****<-*****
		//Zk
		{
			Zk_heading 	= 	NAV_Data_Full_p->KF.Zk[6];
			
			Zk_vn[0] 	= 	NAV_Data_Full_p->KF.Zk[0];
			Zk_vn[1] 	= 	NAV_Data_Full_p->KF.Zk[1];
			Zk_vn[2] 	= 	NAV_Data_Full_p->KF.Zk[2];
			
			Zk_pos[0] 	= 	NAV_Data_Full_p->KF.Zk[3];
			Zk_pos[1] 	= 	NAV_Data_Full_p->KF.Zk[4];
			Zk_pos[2] 	= 	NAV_Data_Full_p->KF.Zk[5];	
			//Zk_pos[0] = NAV_Data_Full_p->KF.Zk[3]*NAV_Data_Full_p->EARTH.RMh;
			//Zk_pos[1] = NAV_Data_Full_p->KF.Zk[4]*NAV_Data_Full_p->EARTH.clRNh;
			//Zk_pos[2] = NAV_Data_Full_p->KF.Zk[5];									
		}
		
	}
	//R�����������
	{	
//		if(E_NAV_SUPPORT_RTK_FIEX_STATUS == NAV_Data_Full_p->Gnss_Use_Status)
//		{
//			SetGnssKalmanRmatrix(E_GPS_RTK_FIXED);
//		}
//		else if(E_NAV_SUPPORT_RTK_ALL_STATUS	==	NAV_Data_Full_p->Gnss_Use_Status)
//		{
//			SetGnssKalmanRmatrix(NAV_Data_Full_p->GPS.rtkStatus);
//		}
		SetGnssKalmanRmatrix(NAV_Data_Full_p);
	}							
}

//���㳵�����ټ��˶�ģ��Լ��
void WheelObsCalAndNoiseSet(_NAV_Data_Full_t* NAV_Data_Full_p)
{	
	int i,j,k;
 double temp = 0.0;
	double temp_V3[3]= {0};
	double lever[3] = {0};
	double wheel_speed = 0;
	double att_m2b[3] = {0};
	double qnb_m2b[4]={0};
	double Cm2b[3*3]={0};
	double vm[3] = {0};
	double vmxz[2] = {0};
	double askew_vn[3*3]={0};
	
		matmul("TN", 3, 3, 3, 1.0, NAV_Data_Full_p->SubKF.Cmb, NAV_Data_Full_p->SINS.Cn2b, 0.0, Cn2m);
		Mat_Tr(3,Cn2m,Cm2n);
	//*******************************************
		askew(NAV_Data_Full_p->SINS.vn, askew_vn);//���Գ���
		matmul("NN",3,3,3,-1.0,Cn2m,askew_vn,0.0,Cn2m_vn);
		Mat_Tr(3,Cn2m_vn,Cn2m_vn_T);
	//************NHC������ϵ������******
		for(j=0;j<3;j++)
		{
			Hk_NHC_Cn2m_vn[0+j*2]=Cn2m_vn[0+3*j];
			Hk_NHC_Cn2m[0+j*2]=Cn2m[0+3*j];
		}
		for(j=0;j<3;j++)
		{
			Hk_NHC_Cn2m_vn[1+j*2]=Cn2m_vn[2+3*j];
			Hk_NHC_Cn2m[1+j*2]=Cn2m[2+3*j];
		}
	//***********ת��****
		for(i=0;i<2;i++)
		{
			for(j=0;j<3;j++)
			{
				Hk_NHC_Cn2m_vn_T[j+3*i]=Hk_NHC_Cn2m_vn[i+j*2];
				Hk_NHC_Cn2m_T[j+3*i]=Hk_NHC_Cn2m[i+j*2];
			}
		}
	//***********************************************************
			//*************20240722************Guolong Zhang************->***********
			for(i = 0;i<3;i++)
			{
				temp = 0;
				for(k = 0;k<3;k++)
				{
					temp += Cn2m[i+k*3]*NAV_Data_Full_p->SINS.vn[k];
					//temp += Cn2m[i + k * 3] * (NAV_Data_Full_p->SINS_buffer[pnum- wheel_time_delay].vn[k] + buffer33_3[i]);
				}
				vm[i] =  temp;							
			}
		//************ODO****
	if(NAV_Data_Full_p->KF.measure_flag_Wheel == RETURN_SUCESS)
	{
		wheel_speed = NAV_Data_Full_p->ODS.scale_factor_filte*NAV_Data_Full_p->ODS.WheelSpeed_ave;
		Zk_m_s = vm[1]-wheel_speed;
		R_m_s = WHEEL_VEL_VAR2 ;
	}
  //*******NHC****
			Zk_m_xz[0] =  vm[0]-0.0;
			Zk_m_xz[1] =  vm[2]-0.0;	
	        R_m_xz[0] = WHEEL_VEL_VAR;
			R_m_xz[1] = WHEEL_VEL_VAR;
	//***********ת�����******
	if(NAV_Data_Full_p->TurnningFlag)
	{
			R_m_s = 4*WHEEL_VEL_VAR2 ;
			R_m_xz[0] =4* WHEEL_VEL_VAR;
			R_m_xz[1] = 4*WHEEL_VEL_VAR;
	}
	//*************20240722************Guolong Zhang************<-***********
	//**************************************************************************************************************************************
	//��װ����Cbm
	//if(gps_pre_location[0]!=NAV_Data_Full_p->GPS.Lat || gps_pre_location[1]!=NAV_Data_Full_p->GPS.Lon)
	//{
	//location_move=sqrt((NAV_Data_Full_p->GPS.Lat-gps_pre_location[0])*(NAV_Data_Full_p->GPS.Lat-gps_pre_location[0])*111320*111320+(NAV_Data_Full_p->GPS.Lon-gps_pre_location[1])*(NAV_Data_Full_p->GPS.Lon-gps_pre_location[1])*100000*100000);
	//inav_log(INAVMD(LOG_ERR),"distance=[%f](m),%f,%f",location_move,gps_pre_location[0],NAV_Data_Full_p->GPS.Lat);
	//}
	
	{			
		
	#if 0
		//��װ�ǶȲ��������к����ʹ����������ϵ�ٶȼ���
		att_m2b[0] 	= 	NAV_Data_Full_p->Param.OD_Att_from_vehicle[0]*DEG2RAD;	
		att_m2b[1] 	= 	NAV_Data_Full_p->Param.OD_Att_from_vehicle[1]*DEG2RAD;
		att_m2b[2] 	= 	NAV_Data_Full_p->Param.OD_Att_from_vehicle[2]*DEG2RAD;
		if (att_m2b[0] == 0 || att_m2b[2] == 0)
		{
			att_m2b[0] 	= 	NAV_Data_Full_p->ODS.att_ods2_b_filte[0]*DEG2RAD;
			att_m2b[2] = NAV_Data_Full_p->ODS.att_ods2_b_filte[2] * DEG2RAD - NAV_Data_Full_p->SINS.wnb[2]/2 * TS;
		}

		//�������ټ�����������ǰ���ٶȣ���Ҫ���ٶȳ��Կ̶�����
      //wheel_speed=NAV_Data_Full_p->ODS.scale_factor_filte*NAV_Data_Full_p->ODS.WheelSpeed_ave;
//		wheel_speed=WheelSpeedOptimize(NAV_Data_Full_p->ODS.WheelSpeed_Front_Left*KMPH2MPS,
//			NAV_Data_Full_p->ODS.WheelSpeed_Front_Right*KMPH2MPS,
//			NAV_Data_Full_p->ODS.WheelSpeed_Back_Left*KMPH2MPS,
//			NAV_Data_Full_p->ODS.WheelSpeed_Back_Right*KMPH2MPS,
//			NAV_Data_Full_p->ODS.scale_factor_filte,// scale_factor_filte;scale_factor
//			&g_valid_wheelspeed);

#if 0
		Scalar_KalmanFilte(&g_wheel_vel,wheel_speed,&P_wheel_vel,Q_wheel_vel,R_wheel_vel,20.0/SAMPLE_FREQ);
		wheel_speed=g_wheel_vel;
#endif
		//Ŀǰ�жϳ��˺��ˣ�����ǰ������N,P,D��λ
//		//�������
//		if(NAV_Data_Full_p->ODS.Gear == E_ODS_GEAR_BACKWARD)
//		{			
//			wheel_speed=-wheel_speed;
//		}				
//		else if(E_ODS_GEAR_STATIC == NAV_Data_Full_p->ODS.Gear )
//		{
//			//wheel_speed= 0;
//		}
//		else if(E_ODS_GEAR_FORWARD == NAV_Data_Full_p->ODS.Gear )
//		{
//		}
//		else
//		{
//#ifdef linux
//			inav_log(INAVMD(LOG_ERR)," error ODS.Gear =%d",NAV_Data_Full_p->ODS.Gear);
//#endif			
//			//wheel_speed= 0;			
//		}		

		//����ϵ��mϵ��̬����
		Cn2m[0] =               NAV_Data_Full_p->SINS.Cn2b[0] + (-att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[1] + ( att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[2];
		Cn2m[1] = ( att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[0] +               NAV_Data_Full_p->SINS.Cn2b[1] + (-att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[2];
		Cn2m[2] = (-att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[0] + ( att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[1] +               NAV_Data_Full_p->SINS.Cn2b[2];
	
		Cn2m[0+3] =               NAV_Data_Full_p->SINS.Cn2b[0+3] + (-att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[1+3] + ( att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[2+3];
		Cn2m[1+3] = ( att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[0+3] +               NAV_Data_Full_p->SINS.Cn2b[1+3] + (-att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[2+3];
		Cn2m[2+3] = (-att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[0+3] + ( att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[1+3] +               NAV_Data_Full_p->SINS.Cn2b[2+3];
		
		Cn2m[0+6] =               NAV_Data_Full_p->SINS.Cn2b[0+6] + (-att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[1+6] + ( att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[2+6];
		Cn2m[1+6] = ( att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[0+6] +               NAV_Data_Full_p->SINS.Cn2b[1+6] + (-att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[2+6];
		Cn2m[2+6] = (-att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[0+6] + ( att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[1+6] +               NAV_Data_Full_p->SINS.Cn2b[2+6];
#if 0
		//Cn2m=Cn2b*Cb2m
		//��֪��̬������Ԫ��
		att2qnb(att_m2b, qnb_m2b);
		//��Ԫ�ؼ���Cm2b
		Qnb2Cnb(qnb_m2b,Cm2b);
		//Cn2m=Cn2b*(Cm2b)'
		matmul("NT", 3, 3, 3, 1.0, NAV_Data_Full_p->SINS.Cn2b, Cm2b, 0.0, Cn2m);
#endif

#endif

//  matmul("TN", 3, 3, 3, 1.0, NAV_Data_Full_p->SubKF.Cmb, NAV_Data_Full_p->SINS.Cn2b, 0.0, Cn2m);
//		Mat_Tr(3,Cn2m,Cm2n);
		//Ln2m��Lm2n��Ln2m��ȥ������ǰ��XZ�����任
//		{
//			for(k = 0;k<3;k++)
//			{
//				Ln2m[0+2*k] = Cn2m[0+3*k];
//				Ln2m[1+2*k] = Cn2m[2+3*k];
//				Ln2m_T[k] = Cm2n[k];																		
//				Ln2m_T[k+3] = Cm2n[k+6];
//			}
//	  }
		//Cn2m*[fn X],����mϵ�ٶ�
#if 0			
		for(i = 0;i<3;i++)
		{
			temp = 0;
			for(k = 0;k<3;k++)
			{
				temp+=  Cn2m[i + 3*k]*NAV_Data_Full_p->SINS.vn[k];
			}
			temp_V3[i] = temp;
		}
#endif	
		//********������������****
/*
		matmul("NN",3,1,3,1.0,Cn2m,NAV_Data_Full_p->SINS.vn,0.0,temp_V3);
		//�󷴶Գ����ת��
	    Cn2m_vn[0] = 0;
	    Cn2m_vn[1] = -temp_V3[2];
	    Cn2m_vn[2] = temp_V3[1];
		
	    Cn2m_vn[0+3] = temp_V3[2];
	    Cn2m_vn[1+3] = 0;
	    Cn2m_vn[2+3] = -temp_V3[0];
		
	    Cn2m_vn[0+6] = -temp_V3[1];
	    Cn2m_vn[1+6] =  temp_V3[0];
	    Cn2m_vn[2+6] = 0;

		Mat_Tr(3,Cn2m_vn,Cn2m_vn_T);
*/
	}
	
	
		
	//wheel
	{
		//�۲�����ֵ
		{	
		//	int pnum = 0;
		//	int wheel_time_delay =8;//����ʱ���ӳ�
//			double mb_lever[3] = { -0.15,-0.5,-1.5 };//imu�������������ĵĸ˱�
//			double buffer33[3*3] = { 0 };//�洢��ʱ�������Գƾ���
//			double buffer33_2[3*3] = { 0 };//�洢��ʱ����
//			double buffer33_3[3] = { 0 };//�洢��ʱ����
//			if (NAV_Data_Full_p->Head < wheel_time_delay)
//			{
//				pnum = NAV_Data_Full_p->Head + 50;
//			}
//			else pnum = NAV_Data_Full_p->Head;
			//askew(NAV_Data_Full_p->SINS.wnb, buffer33);//���Գƾ���
			//matmul("NN", 3, 3, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, buffer33, 0.0, buffer33_2);
			//matmul("NN", 3, 1, 3, 1.0, buffer33_2, mb_lever, 0.0, buffer33_3);//�˱��ٶ����
				
//			for(i = 0;i<3;i++)
//			{
//				temp = 0;
//				for(k = 0;k<3;k++)
//				{
//					temp += Cn2m[i+k*3]*NAV_Data_Full_p->SINS.vn[k];
//					//temp += Cn2m[i + k * 3] * (NAV_Data_Full_p->SINS_buffer[pnum- wheel_time_delay].vn[k] + buffer33_3[i]);
//				}
//				vm[i] =  temp;							
//			}
	
			//�۲���Ϊ�ٶȲв�
//			Zk_m[0] =  vm[0]-0.0;
//			Zk_m[1] =  vm[1]-wheel_speed;
//			Zk_m[2] =  vm[2]-0.0;	
			
			//Zk_m=Zk_m+Cn2m_vn*att_m2b
//			if(0)
//			{
//				matmul("NN",3,1,3,1.0,Cn2m_vn,att_m2b,1.0,Zk_m);
//			}
#if 0				
			//����ת�������ǣ���˶�
			{
				v_OB_IMU[0] = (-NAV_Data_Full_p->SINS.web[2])*NAV_Data_Full_p->Param.OD_ArmLength[1]
							 +( NAV_Data_Full_p->SINS.web[1])*NAV_Data_Full_p->Param.OD_ArmLength[2];
				v_OB_IMU[1] = ( NAV_Data_Full_p->SINS.web[2])*NAV_Data_Full_p->Param.OD_ArmLength[0]
							 +(-NAV_Data_Full_p->SINS.web[0])*NAV_Data_Full_p->Param.OD_ArmLength[2];
				v_OB_IMU[2] = (-NAV_Data_Full_p->SINS.web[1])*NAV_Data_Full_p->Param.OD_ArmLength[0]
							 +( NAV_Data_Full_p->SINS.web[0])*NAV_Data_Full_p->Param.OD_ArmLength[1];							
				Zk_m[0] = Zk_m[0] - v_OB_IMU[0];
				Zk_m[1] = Zk_m[1] - v_OB_IMU[1];						
				Zk_m[2] = Zk_m[2] - v_OB_IMU[2];							
			}
#endif				
			
		}
//		if (fabs(NAV_Data_Full_p->IMU.acc_use[2] - NAV_Data_Full_p->IMU.acc_use_pre[2])>3)
//		{
//			quake_count = 15;
//		}
//		quake_count--;
		#if 0
		//��������
		{
			//R_m[0] = 0.1*0.1;
			//R_m[1] = 0.1*0.1;
			//R_m[2] = 0.1*0.1;

			//R_m[0] = WHEEL_VEL_VAR + (0.1 * sin(NAV_Data_Full_p->SINS.att[2]))*(0.1*sin(NAV_Data_Full_p->SINS.att[2]));
			//R_m[1] = WHEEL_VEL_VAR + (0.1 * cos(NAV_Data_Full_p->SINS.att[2]))*(0.1*cos(NAV_Data_Full_p->SINS.att[2]));
			//R_m[2] = WHEEL_VEL_VAR*1.4;
			

			//double Rm_error[9] = { 0};
			//Rm_error[0] = WHEEL_VEL_VAR;
			//Rm_error[4] = WHEEL_VEL_VAR;
			//Rm_error[8] = WHEEL_VEL_VAR;
			

			//if (fabs(NAV_Data_Full_p->SINS.wnb[2]) > 20 * DEG2RAD)
			{
				//Rm_error[0] = WHEEL_VEL_VAR * 36;
			}
			//else if (fabs(NAV_Data_Full_p->SINS.wnb[2]) > 10 * DEG2RAD)
			{
				//Rm_error[0] = WHEEL_VEL_VAR * 16;
			}
			//else if (fabs(NAV_Data_Full_p->SINS.wnb[2]) > 6 * DEG2RAD)
			{
				//Rm_error[0] = WHEEL_VEL_VAR * 9;
			}
			//else if (fabs(NAV_Data_Full_p->SINS.wnb[2]) > 3 * DEG2RAD)
			{
				//Rm_error[0] = WHEEL_VEL_VAR * 4;
			}


			//double Rm_error2[9] = { 0 };
			//matmul("NN", 3, 3, 3, 1.0, Cm2n, Rm_error, 0.0, Rm_error2);
			//matmul("NT", 3, 3, 3, 1.0, Rm_error2, Cm2n, 0.0, Rm_error);

			R_m[0] = WHEEL_VEL_VAR ;
			R_m[1] = WHEEL_VEL_VAR2 ;
			R_m[2] = WHEEL_VEL_VAR ;
//			if (NAV_Data_Full.KF.use_gps_flag == E_FUNSION_WHEEL)
//			{
//				
//				wheellcount++;
//			}
			/*if (combineData.imuSelect == 0
				&& NAV_Data_Full_p->ODS.WheelSpeed_Back_Right !=0
				)
			{
				R_m[0] = WHEEL_VEL_VAR *5;
				R_m[1] = WHEEL_VEL_VAR*5;
				R_m[2] = WHEEL_VEL_VAR * 10;
			}*/
			if (wheel_speed == 0)
			{
				R_m[0] = WHEEL_VEL_VAR * 0.01;
				R_m[1] = WHEEL_VEL_VAR2 * 0.01;
				R_m[2] = WHEEL_VEL_VAR * 0.01;
			}
#if 0
			double deltaZ = 0;
			double sigma = 0;

			if (fabs(NAV_Data_Full_p->SINS.wnb[2]) <1 * DEG2RAD)
			{
				deltaZ = 1;
			}
			if (fabs(NAV_Data_Full_p->SINS.wnb[2]) > 1 * DEG2RAD)
			{
				deltaZ = 1;
			}
			if (fabs(NAV_Data_Full_p->SINS.wnb[2]) > 5 * DEG2RAD)
			{
				deltaZ = 5;
			}
			if (fabs(NAV_Data_Full_p->SINS.wnb[2]) > 10 * DEG2RAD)
			{
				deltaZ = 10;
			}
			if (fabs(NAV_Data_Full_p->SINS.wnb[2]) > 20 * DEG2RAD)
			{
				deltaZ = 20;
			}
#endif			
			
			


			//R_m[0] = Rm_error[0];
			//R_m[1] = Rm_error[4];
			//R_m[2] = Rm_error[8];

			/*R_m2[0] = WHEEL_VEL_VAR;
			R_m2[1] = WHEEL_VEL_VAR2;
			R_m2[2] = WHEEL_VEL_VAR;*/


		}
		#endif
	}
#if 0
	//�˶�Լ��ģ��***********************
	{
		//�۲�����ֵ
		{
			for(i = 0;i<2;i++)
			{
				temp = 0;
				for(k = 0;k<3;k++)
				{
					temp += Ln2m[i+2*k]*NAV_Data_Full_p->SINS.vn[k];
				}
				vmxz[i] =  temp;							
			}
			
			Zk_m_xz[0] =  vmxz[0];
			Zk_m_xz[1] =  vmxz[1];
#if 0
			if(0)//����ת�������ǣ���˶�
			{
				v_OB_IMU[0] = (-NAV_Data_Full_p->SINS.web[2])*NAV_Data_Full_p->Param.OD_ArmLength[1]
							 +( NAV_Data_Full_p->SINS.web[1])*NAV_Data_Full_p->Param.OD_ArmLength[2];
				v_OB_IMU[1] = ( NAV_Data_Full_p->SINS.web[2])*NAV_Data_Full_p->Param.OD_ArmLength[0]
							 +(-NAV_Data_Full_p->SINS.web[0])*NAV_Data_Full_p->Param.OD_ArmLength[2];
				v_OB_IMU[2] = (-NAV_Data_Full_p->SINS.web[1])*NAV_Data_Full_p->Param.OD_ArmLength[0]
							 +( NAV_Data_Full_p->SINS.web[0])*NAV_Data_Full_p->Param.OD_ArmLength[1];							
				Zk_m_xz[0] = Zk_m_xz[0] - v_OB_IMU[0];					
				Zk_m_xz[1] = Zk_m_xz[1] - v_OB_IMU[2];							
			}	
#endif				
		}
		//��������
		{
			R_m_xz[0] = WHEEL_VEL_VAR;
			R_m_xz[1] = WHEEL_VEL_VAR;							
		}						
	}
	//�˶�Լ��ģ��***********************
 #endif
	

}


void ObsCalAndObsNoiseSet(_NAV_Data_Full_t* NAV_Data_Full_p)
{	
	//GPS�۲�������
	//GPS�����ݸ���
	//if((E_GPS_IS_UPDATE == NAV_Data_Full_p->GPS.gps_up_flag) && (NAV_Data_Full_p->GPS.headingStatus		== E_GPS_RTK_FIXED))
//	if (//((NAV_Data_Full_p->KF.measure_flag_vn) || (NAV_Data_Full_p->KF.measure_flag_pos)||(NAV_Data_Full_p->KF.measure_flag_head))&&
	//   (E_GPS_IS_UPDATE == NAV_Data_Full_p->GPS.gps_up_flag))
//	{
		    GPSObsCalAndNoiseSet(NAV_Data_Full_p);
//	}
	
	//if ((E_GPS_IS_UPDATE == NAV_Data_Full_p->GPS.gps_up_flag))
	//	GPSObsCalAndNoiseSet(NAV_Data_Full_p);
	//���㳵�����ټ��˶�ģ��Լ��
	if((NAV_Data_Full_p->KF.measure_flag_NHC	== RETURN_SUCESS) || (NAV_Data_Full_p->KF.measure_flag_Wheel== RETURN_SUCESS))
	{
			WheelObsCalAndNoiseSet(NAV_Data_Full_p);
	}
	//*********ZUPT*******************************
	if(NAV_Data_Full_p->ZUPT_flag == RETURN_SUCESS)
	{
		Zk_ZUPT[0]=NAV_Data_Full_p->SINS.vn[0];
		Zk_ZUPT[1]=NAV_Data_Full_p->SINS.vn[1];
		Zk_ZUPT[2]=NAV_Data_Full_p->SINS.vn[2];
	}
	//*********Guolong Zhang******20241227****���ڱ궨�Ż�****
//	if(NAV_Data_Full_p ->Nav_Status== E_NAV_STATUS_SYSTEM_STANDARD)
	//{
	//	 NAV_Data_Full_p->KF.measure_flag_head	=	E_KALMAN_MEASURE_HEADING_NO;
	//}
	//********heading���������ϵ����*********************
	if ((NAV_Data_Full_p->ZUPT_flag) && 
		(E_KALMAN_MEASURE_HEADING_NO==NAV_Data_Full_p->KF.measure_flag_head)
		)
	{
		NAV_Data_Full_p->ZUPTyaw_ST_Cnt++;
		if (NAV_Data_Full_p->ZUPTyaw_ST_Cnt == ZUPT_DampingHeading_Size)
		{
			ZkPre_ZUPT_heading = NAV_Data_Full_p->SINS.att[2];//�׸������ȡ
		}
		double tmp_pit = 0.0;
		tmp_pit = NAV_Data_Full_p->SINS.att[0];
		if (tmp_pit > TH_pit)
		{
			tmp_pit = TH_pit;
		}
		if (tmp_pit < -TH_pit)
		{
			tmp_pit = -TH_pit;
		}
		ZUPT_Hk_head[0] = -tan(tmp_pit) * sin(NAV_Data_Full_p->SINS.att[2]);
		ZUPT_Hk_head[1] = tan(tmp_pit) * cos(NAV_Data_Full_p->SINS.att[2]);
		ZUPT_Hk_head[2] = -1.0;
	 Zk_ZUPT_Heading = NAV_Data_Full_p->SINS.att[2] - ZkPre_ZUPT_heading;//******����������ʵʱ����*********
	}
	else
	{
		NAV_Data_Full_p->ZUPTyaw_ST_Cnt = 0;//*******���»�ȡZUPT����ֵ***********
	}
//*******************************************************

#if 0	
	//ע��˺������鲻�ټӾֲ���������������Խ��
	int i,j,k;
    double temp = 0;
	double temp_V3[3]= {0};
	double GnssPosition[3] = {0};
	double GnssVel[3] = {0};
	double Gnss_heading = 0;
	double lever[3] = {0};
	double wheel_speed = 0;
	double att_m2b[3] = {0};
	double qnb_m2b[4]={0};
	double Cm2b[3*3]={0};
	double vm[3] = {0};
	//double v_OB_IMU[3] = {0}; //����ת�������ǣ���˶�
	double vmxz[2] = {0};
	//GPS
	{
		//GPS�����ݸ���
		if(E_GPS_IS_UPDATE == NAV_Data_Full_p->GPS.gps_up_flag)	
		{ 							
			//�۲�����ֵ
			{	
				//GPS��λ��
				GnssPosition[0]	=	NAV_Data_Full_p->GPS.Lat * DEG2RAD;//��λrad
				GnssPosition[1]	=	NAV_Data_Full_p->GPS.Lon * DEG2RAD;//��λrad
				GnssPosition[2]	=	NAV_Data_Full_p->GPS.Altitude;//�̵߳�λm 	
				GnssVel[0] = NAV_Data_Full_p->GPS.ve; 
				GnssVel[1] = NAV_Data_Full_p->GPS.vn; 
				GnssVel[2] = NAV_Data_Full_p->GPS.vu; 
				//�˱�ʸ��,��λm
				lever[0] = NAV_Data_Full_p->Param.gnssArmLength[0]; 
				lever[1] = NAV_Data_Full_p->Param.gnssArmLength[1]; 
				lever[2] = NAV_Data_Full_p->Param.gnssArmLength[2]; 
								
				//GPS���ݱ۸˲����Լ�ʱ��ͬ������
				{
					//����GPSʱ�䲹��
					{								
						NAV_Data_Full_p->GPS.delay = NAV_Data_Full_p->GPS.delay_pps + g_KF_UP2_gps_delay_cunt*NAV_Data_Full_p->SINS.ts;  //0.005s
						g_KF_UP2_gps_delay_cunt = 0;
						//NAV_Data_Full_p->GPS.delay = CalGPSTimeCompensate(NAV_Data_Full_p);
					}
					
					//�۲⺽���
					if(E_NAV_STATUS_IN_NAV == NAV_Data_Full_p->Nav_Status)
					{
						Gnss_heading = CorrHeading((-NAV_Data_Full_p->GPS.Heading
										 + NAV_Data_Full_p->Param.gnssAtt_from_vehicle[2]
										 + NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2]
						                )*DEG2RAD);	
						LeverarmTimeCorr2( NAV_Data_Full_p->KF.Zk,
									  NAV_Data_Full_p,
									  Gnss_heading,
									  lever,
									  GnssVel,
									  GnssPosition,
									  NAV_Data_Full_p->GPS.delay);	
						
#if 0						
						if(fabs(NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG)<2.0f)
						{
							NAV_Data_Full_p->KF.RTK_Heading_OK = 1;
						}
#endif						
					}
					else if(E_NAV_STATUS_SYSTEM_STANDARD ==  NAV_Data_Full_p->Nav_Status)
					{
						Gnss_heading = CorrHeading((-NAV_Data_Full_p->GPS.Heading+ NAV_Data_Full_p->Param.gnssAtt_from_vehicle[2])*DEG2RAD);									  								
						//�����ٶȡ�λ�òв���� 
						LeverarmTimeCorr2( NAV_Data_Full_p->KF.Zk,
										  NAV_Data_Full_p,
										  Gnss_heading,
										  lever,
										  GnssVel,
										  GnssPosition,
										  NAV_Data_Full_p->GPS.delay);
						//��ʾû��ת��ʱ��
						if((fabs(NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG)<2.0f)//2.0f
						   )
						{
							//Scalar_KalmanFilte(&NAV_Data_Full.Param.gnssAtt_from_vehicle2[2],NAV_Data_Full_p->KF.Zk[6]*RAD2DEG,&P_ve_Cvn,Q_ve_Cvn,R_ve_Cvn);
							//Zk����̬�仯???�Ƿ������˲���
							NAV_Data_Full.Param.gnssAtt_from_vehicle2[2] = NAV_Data_Full_p->KF.Zk[6]*RAD2DEG;		
						}
						
					}

					
				}
				//Zk
				{
					Zk_heading = NAV_Data_Full_p->KF.Zk[6];
					
					Zk_vn[0] = NAV_Data_Full_p->KF.Zk[0];
					Zk_vn[1] = NAV_Data_Full_p->KF.Zk[1];
					Zk_vn[2] = NAV_Data_Full_p->KF.Zk[2];
					
					Zk_pos[0] = NAV_Data_Full_p->KF.Zk[3];
					Zk_pos[1] = NAV_Data_Full_p->KF.Zk[4];
					Zk_pos[2] = NAV_Data_Full_p->KF.Zk[5];	
					
					//Zk_pos[0] = NAV_Data_Full_p->KF.Zk[3]*NAV_Data_Full_p->EARTH.RMh;
					//Zk_pos[1] = NAV_Data_Full_p->KF.Zk[4]*NAV_Data_Full_p->EARTH.clRNh;
					//Zk_pos[2] = NAV_Data_Full_p->KF.Zk[5];									
				}
				
			}
			//��������
			{
				if((NAV_Data_Full_p->debug & 0x01) != 0x01)//����ģʽ
				{
					if(E_NAV_SUPPORT_RTK_FIEX_STATUS == NAV_Data_Full_p->Gnss_Use_Status)
					{
						SetGnssKalmanRmatrix(E_GPS_RTK_FIXED);
					}
					else if(E_NAV_SUPPORT_RTK_ALL_STATUS	==	NAV_Data_Full_p->Gnss_Use_Status)
					{
						SetGnssKalmanRmatrix(NAV_Data_Full_p->GPS.rtkStatus);
					}
					
			    }	
				else //debugģʽ
				{
					R_heading = RTK_HEAD_VAR;
						
					R_vn[0] = VEL_VAR2;//RTK_VEL_VAR;//
					R_vn[1] = VEL_VAR2;//RTK_VEL_VAR;//
					R_vn[2] = VEL_VAR2;//RTK_VEL_VAR;//
						
					R_pos[0] = RTK_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
					R_pos[1] = RTK_POS_LON_LAT_VAR2;//SPP_POS_VAR2;
					R_pos[2] = RTK_POS_HEIGHT_VAR2;											
				}									
			}							
		}
	}					
	//wheel+Լ��
	{	
		//��װ����Cbm
		{			
			//��װ�ǶȲ��������к����ʹ����������ϵ�ٶȼ���
			att_m2b[0] = NAV_Data_Full_p->Param.OD_Att_from_vehicle[0]*DEG2RAD;
			//att_m2b[0] = NAV_Data_Full_p->ODS.att_ods2_b_filte[0]*DEG2RAD;	
			att_m2b[1] = NAV_Data_Full_p->Param.OD_Att_from_vehicle[1]*DEG2RAD;
			att_m2b[2] = NAV_Data_Full_p->ODS.att_ods2_b_filte[2]*DEG2RAD;					

			//�������ټ�����������ǰ���ٶȣ���Ҫ���ٶȳ��Կ̶�����
			wheel_speed=WheelSpeedOptimize(NAV_Data_Full_p->ODS.WheelSpeed_Front_Left*KMPH2MPS,
					NAV_Data_Full_p->ODS.WheelSpeed_Front_Left*KMPH2MPS,
					NAV_Data_Full_p->ODS.WheelSpeed_Back_Left*KMPH2MPS,
					NAV_Data_Full_p->ODS.WheelSpeed_Back_Right*KMPH2MPS,
					NAV_Data_Full_p->ODS.scale_factor_filte,// scale_factor_filte;scale_factor
					&g_valid_wheelspeed);
#if 0
			Scalar_KalmanFilte(&g_wheel_vel,wheel_speed,&P_wheel_vel,Q_wheel_vel,R_wheel_vel,20.0/SAMPLE_FREQ);
			wheel_speed=g_wheel_vel;
#endif
			//�������
			if(NAV_Data_Full_p->ODS.Gear == E_ODS_GEAR_BACKWARD)
			{
#if 0			
				wheel_speed = -(   NAV_Data_Full_p->ODS.WheelSpeed_Front_Left
								+ NAV_Data_Full_p->ODS.WheelSpeed_Front_Right
								+ NAV_Data_Full_p->ODS.WheelSpeed_Back_Left
								+ NAV_Data_Full_p->ODS.WheelSpeed_Back_Right										
							   )*0.25f*KMPH2MPS*NAV_Data_Full_p->ODS.scale_factor;	
#endif				
				wheel_speed=-wheel_speed;
			}				
			else if(E_ODS_GEAR_STATIC == NAV_Data_Full_p->ODS.Gear )
			{
				//wheel_speed= 0;
			}
			else if(E_ODS_GEAR_FORWARD == NAV_Data_Full_p->ODS.Gear )
			{
			}
			else
			{
#ifdef linux
				inav_log(INAVMD(LOG_ERR)," error ODS.Gear =%d",NAV_Data_Full_p->ODS.Gear);
#endif			
				//wheel_speed= 0;			
			}		

			//����ϵ��mϵ��̬����
			Cn2m[0] =               NAV_Data_Full_p->SINS.Cn2b[0] + (-att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[1] + ( att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[2];
			Cn2m[1] = ( att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[0] +               NAV_Data_Full_p->SINS.Cn2b[1] + (-att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[2];
			Cn2m[2] = (-att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[0] + ( att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[1] +               NAV_Data_Full_p->SINS.Cn2b[2];
		
			Cn2m[0+3] =               NAV_Data_Full_p->SINS.Cn2b[0+3] + (-att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[1+3] + ( att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[2+3];
			Cn2m[1+3] = ( att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[0+3] +               NAV_Data_Full_p->SINS.Cn2b[1+3] + (-att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[2+3];
			Cn2m[2+3] = (-att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[0+3] + ( att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[1+3] +               NAV_Data_Full_p->SINS.Cn2b[2+3];
			
			Cn2m[0+6] =               NAV_Data_Full_p->SINS.Cn2b[0+6] + (-att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[1+6] + ( att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[2+6];
			Cn2m[1+6] = ( att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[0+6] +               NAV_Data_Full_p->SINS.Cn2b[1+6] + (-att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[2+6];
			Cn2m[2+6] = (-att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[0+6] + ( att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[1+6] +               NAV_Data_Full_p->SINS.Cn2b[2+6];


#if 0
			//Cn2m=Cn2b*Cb2m
			//��֪��̬������Ԫ��
			att2qnb(att_m2b, qnb_m2b);
			//��Ԫ�ؼ���Cm2b
			Qnb2Cnb(qnb_m2b,Cm2b);
			//Cn2m=Cn2b*(Cm2b)'
			matmul("NT", 3, 3, 3, 1.0, NAV_Data_Full_p->SINS.Cn2b, Cm2b, 0.0, Cn2m);
#endif
			Mat_Tr(3,Cn2m,Cm2n);
			//Ln2m��Lm2n��Ln2m��ȥ������ǰ��XZ�����任
			{
				for(k = 0;k<3;k++)
				{
					Ln2m[0+2*k] = Cn2m[0+3*k];
					Ln2m[1+2*k] = Cn2m[2+3*k];
					Ln2m_T[k] = Cm2n[k];																		
					Ln2m_T[k+3] = Cm2n[k+6];
				}
		    }
			//Cn2m*[fn X],����mϵ�ٶ�
#if 0			
			for(i = 0;i<3;i++)
			{
				temp = 0;
				for(k = 0;k<3;k++)
				{
					temp+=  Cn2m[i + 3*k]*NAV_Data_Full_p->SINS.vn[k];
				}
				temp_V3[i] = temp;
			}
#endif			
			matmul("NN",3,1,3,1.0,Cn2m,NAV_Data_Full_p->SINS.vn,0.0,temp_V3);

			//�󷴶Գ����ת��
		    Cn2m_vn[0] = 0;
		    Cn2m_vn[1] = -temp_V3[2];
		    Cn2m_vn[2] = temp_V3[1];
			
		    Cn2m_vn[0+3] = temp_V3[2];
		    Cn2m_vn[1+3] = 0;
		    Cn2m_vn[2+3] = -temp_V3[0];
			
		    Cn2m_vn[0+6] = -temp_V3[1];
		    Cn2m_vn[1+6] =  temp_V3[0];
		    Cn2m_vn[2+6] = 0;

			Mat_Tr(3,Cn2m_vn,Cn2m_vn_T);
		}
		//wheel
		{
			//�۲�����ֵ
			{
				for(i = 0;i<3;i++)
				{
					temp = 0;
					for(k = 0;k<3;k++)
					{
						temp += Cn2m[i+k*3]*NAV_Data_Full_p->SINS.vn[k];
					}
					vm[i] =  temp;							
				}
				//�۲���Ϊ�ٶȲв�
				Zk_m[0] =  vm[0]-0.0;
				Zk_m[1] =  vm[1]-wheel_speed;
				Zk_m[2] =  vm[2]-0.0;	
				//Zk_m=Zk_m+Cn2m_vn*att_m2b
				if(0)
				{
					matmul("NN",3,1,3,1.0,Cn2m_vn,att_m2b,1.0,Zk_m);
				}
#if 0				
				//����ת�������ǣ���˶�
				{
					v_OB_IMU[0] = (-NAV_Data_Full_p->SINS.web[2])*NAV_Data_Full_p->Param.OD_ArmLength[1]
								 +( NAV_Data_Full_p->SINS.web[1])*NAV_Data_Full_p->Param.OD_ArmLength[2];
					v_OB_IMU[1] = ( NAV_Data_Full_p->SINS.web[2])*NAV_Data_Full_p->Param.OD_ArmLength[0]
								 +(-NAV_Data_Full_p->SINS.web[0])*NAV_Data_Full_p->Param.OD_ArmLength[2];
					v_OB_IMU[2] = (-NAV_Data_Full_p->SINS.web[1])*NAV_Data_Full_p->Param.OD_ArmLength[0]
								 +( NAV_Data_Full_p->SINS.web[0])*NAV_Data_Full_p->Param.OD_ArmLength[1];							
					Zk_m[0] = Zk_m[0] - v_OB_IMU[0];
					Zk_m[1] = Zk_m[1] - v_OB_IMU[1];						
					Zk_m[2] = Zk_m[2] - v_OB_IMU[2];							
				}
#endif				
				
			}
			//��������
			{
				//R_m[0] = 0.1*0.1;
				//R_m[1] = 0.1*0.1;
				//R_m[2] = 0.1*0.1;
				R_m[0] = WHEEL_VEL_VAR;
				R_m[1] = WHEEL_VEL_VAR2;
				R_m[2] = WHEEL_VEL_VAR;	
				
			}
			
		}
		//�˶�Լ��ģ��
		{
			//�۲�����ֵ
			{
				for(i = 0;i<2;i++)
				{
					temp = 0;
					for(k = 0;k<3;k++)
					{
						temp += Ln2m[i+2*k]*NAV_Data_Full_p->SINS.vn[k];
					}
					vmxz[i] =  temp;							
				}
				
				Zk_m_xz[0] =  vmxz[0];
				Zk_m_xz[1] =  vmxz[1];
#if 0
				if(0)//����ת�������ǣ���˶�
				{
					v_OB_IMU[0] = (-NAV_Data_Full_p->SINS.web[2])*NAV_Data_Full_p->Param.OD_ArmLength[1]
								 +( NAV_Data_Full_p->SINS.web[1])*NAV_Data_Full_p->Param.OD_ArmLength[2];
					v_OB_IMU[1] = ( NAV_Data_Full_p->SINS.web[2])*NAV_Data_Full_p->Param.OD_ArmLength[0]
								 +(-NAV_Data_Full_p->SINS.web[0])*NAV_Data_Full_p->Param.OD_ArmLength[2];
					v_OB_IMU[2] = (-NAV_Data_Full_p->SINS.web[1])*NAV_Data_Full_p->Param.OD_ArmLength[0]
								 +( NAV_Data_Full_p->SINS.web[0])*NAV_Data_Full_p->Param.OD_ArmLength[1];							
					Zk_m_xz[0] = Zk_m_xz[0] - v_OB_IMU[0];					
					Zk_m_xz[1] = Zk_m_xz[1] - v_OB_IMU[2];							
				}	
#endif				
			}
			//��������
			{
				R_m_xz[0] = 0.1*0.1;
				R_m_xz[1] = 0.1*0.1;							
			}						
		}
	}
#endif	
}




void CheckFusionStatusAndTime()
{
		if(NAV_Data_Full.KF.pre_fusion_source == NAV_Data_Full.KF.fusion_source)
		{
			g_NAV_Funsion_Status_Time.fusion_source	=	NAV_Data_Full.KF.fusion_source;
			if(g_NAV_Funsion_Status_Time.duration>=(10*MAX_WHEEL_MOEDL_DURATION*SAMPLE_FREQ))
			{
				g_NAV_Funsion_Status_Time.duration = 10*MAX_WHEEL_MOEDL_DURATION*SAMPLE_FREQ;
			}
			g_NAV_Funsion_Status_Time.duration++;
			
		}
		else
		{
			g_NAV_Funsion_Status_Time.fusion_source	=	NAV_Data_Full.KF.fusion_source;
			g_NAV_Funsion_Status_Time.duration		=	0;
		}
}
//��ʵ��û��rtk�̶�������������������£�����Լ��ģ�ͼ���һ��ʱ���ʹ�÷�rtkģ������Լ��
void SetGPSFunsionPolicy(_NAV_Funsion_Status_Time_t *p, _NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned int  threhold=0;
	//Ŀǰ��GPS�źţ�����ǹ̶��⣬����GPS�ں�
	if(E_GPS_RTK_FIXED	==	NAV_Data_Full_p->GPS.rtkStatus)
	{
		SetNavFunsionSource(E_FUNSION_GPS);
	}
	else//�������gps�̶��⣬�������
	{
		//�����һ��״̬������ģ�ͣ��鿴����ģ���Ƿ񳬹�������λʱ��
		if(E_FUNSION_WHEEL	==	p->fusion_source)
			
		{	
			countgps = 0;
			threhold = MAX_WHEEL_MOEDL_DURATION*SAMPLE_FREQ;
			if(p->duration >= threhold)
			{
				SetNavFunsionSource(E_FUNSION_GPS);
			}
			else
			{
				SetNavFunsionSource(E_FUNSION_WHEEL);
			}
		}
		else if(E_FUNSION_MOTION	==	p->fusion_source)
		{	countgps = 0;
			threhold = MAX_MOTION_MOEDL_DURATION*SAMPLE_FREQ;
			if(p->duration >= threhold)
			{
				SetNavFunsionSource(E_FUNSION_GPS);
			}
			else
			{
				SetNavFunsionSource(E_FUNSION_MOTION);
			}
		}
		//�����һ��״̬��GPS�ں�ģ��
		else if(E_FUNSION_GPS	==	p->fusion_source)
		{
			//�����һ��״̬���ǹ̶��⣬���������gps�ں�
			if(E_GPS_RTK_FIXED != NAV_Data_Full_p->GPS.pre_rtkStatus)
			{
				SetNavFunsionSource(E_FUNSION_GPS);
			}
			//�����һ��״̬�ǹ̶��⣬��ǰ���Ƿǹ̶����л������ٻ��˶�ģ��
			else
			{
				if(NAV_Data_Full_p->ODS.ods_flag == E_ODS_WHEEL_FLAG_HAVE)//wheel
				{
					SetNavFunsionSource(E_FUNSION_WHEEL);
				}
				else//wheel
				{
					SetNavFunsionSource(E_FUNSION_MOTION);
				}
				
			}
		}
		else
		{
			SetNavFunsionSource(E_FUNSION_GPS);
		}
	}

}
void SetFunsionStatusAndMeasureUpdate(_NAV_Data_Full_t* NAV_Data_Full_p)
{	
	//ͳ����������״̬�µ�ǰ�ں�״̬��������Ԫ��Ŀ,���������ں�״̬
	//CheckFusionStatusAndTime();
	#if 0
	//�ں�Դѡ��
	{					
		if(NAV_Data_Full_p->KF.use_gps_flag == E_FUNSION_GPS)//��GPS�ں�
		{
			//ֻ�ں�rtk��
			if(		E_NAV_SUPPORT_RTK_FIEX_STATUS	==	NAV_Data_Full_p->Gnss_Use_Status	
				&&	(NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) //'A'
				&& 	E_GPS_RTK_FIXED					==	NAV_Data_Full_p->GPS.rtkStatus //RTK	
				
			  )							
			{
				SetNavFunsionSource(E_FUNSION_GPS);
				//NAV_Data_Full_p->KF.fusion_source = E_FUNSION_GPS;	
			}
			//�ں����н�
			else if(		E_NAV_SUPPORT_RTK_ALL_STATUS	==	NAV_Data_Full_p->Gnss_Use_Status	
						&&	(NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) //'A'
						&& 	E_GPS_RTK_INVALID				<	NAV_Data_Full_p->GPS.rtkStatus //RTK	
				
			  )							
			{
#if 0			
				if(E_GPS_RTK_FIXED == NAV_Data_Full_p->GPS.rtkStatus)
				{
					SetNavFunsionSource(E_FUNSION_GPS);
				}
				//����Ƿǹ̶��⣬��Ҫ��ȡ���²���
				//�����ǰ״̬�����ٻ��˶�ģ�ͣ���Ҫ����һ��ʱ�䣬�����ں�ģ������󣬲��÷ǹ̶����GPS�ں�
				else
				{
					SetGPSNoFixFunsionPolicy();
				}
				
				//NAV_Data_Full_p->KF.fusion_source = E_FUNSION_GPS;	
#endif	
				SetGPSFunsionPolicy(&g_NAV_Funsion_Status_Time,NAV_Data_Full_p);
			}
			else //�л��ں�Դ
			{	
				if(NAV_Data_Full_p->ODS.ods_flag == E_ODS_WHEEL_FLAG_HAVE)//wheel
				{
					SetNavFunsionSource(E_FUNSION_WHEEL);
					//NAV_Data_Full_p->KF.fusion_source = E_FUNSION_WHEEL;
				}
				else//wheel
				{
					SetNavFunsionSource(E_FUNSION_MOTION);
					//NAV_Data_Full_p->KF.fusion_source = E_FUNSION_MOTION;
				}							
			}
		}
		else if(NAV_Data_Full_p->KF.use_gps_flag == E_FUNSION_WHEEL)//��wheel�ں�
		{
			if(NAV_Data_Full_p->ODS.ods_flag == E_ODS_WHEEL_FLAG_HAVE)//wheel
			{
				SetNavFunsionSource(E_FUNSION_WHEEL);
				//NAV_Data_Full_p->KF.fusion_source = E_FUNSION_WHEEL;
			}
			else//wheel
			{
				SetNavFunsionSource(E_FUNSION_MOTION);
				//NAV_Data_Full_p->KF.fusion_source = E_FUNSION_MOTION;
			}
		}
		else if(NAV_Data_Full_p->KF.use_gps_flag == E_FUNSION_MOTION)//���˶�Լ��ģ��
		{
			SetNavFunsionSource(E_FUNSION_MOTION);
			//NAV_Data_Full_p->KF.fusion_source = E_FUNSION_MOTION;
		}
		else
		{
			SetNavFunsionSource(E_FUNSION_NONE);
			//NAV_Data_Full_p->KF.fusion_source = E_FUNSION_NONE;
		}
	}
	//�ж��Ƿ��в�������
	{
		//if(NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG < 5)
		if(1)
		{
			if(NAV_Data_Full_p->KF.fusion_source == E_FUNSION_GPS)//GPS
			{
				if(NAV_Data_Full_p->GPS.gps_up_flag == E_GPS_IS_UPDATE)	
				{
					NAV_Data_Full_p->GPS.gps_up_flag = E_GPS_NO_UPDATE;							
					NAV_Data_Full_p->KF.measure_flag = E_KALMAN_MEASURE_UPDATE_HAVE;
				}
				else
				{
					NAV_Data_Full_p->KF.measure_flag = E_KALMAN_MEASURE_UPDATE_NONE;
				}
			}
			//����
			else if(NAV_Data_Full_p->KF.fusion_source == E_FUNSION_WHEEL
				   ||NAV_Data_Full_p->KF.fusion_source == E_FUNSION_MOTION)
			{
				NAV_Data_Full_p->KF.measure_flag = E_KALMAN_MEASURE_UPDATE_HAVE;
			}
		}
		//û���ں�Դ
		else
		{
			NAV_Data_Full_p->KF.measure_flag = E_KALMAN_MEASURE_UPDATE_NONE;
		}
	}
	#endif

	//***********************20240704******Guolong Zhang*****->******
	 if((NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED)
		   &&(NAV_Data_Full_p->GPS.Position_Status != E_GPS_POS_INVALID))
	  { 
							NAV_Data_Full_p->KF.measure_flag_vn=E_KALMAN_MEASURE_VEL_YES;
							NAV_Data_Full_p->KF.measure_flag_pos=E_KALMAN_MEASURE_POS_YES;
						
						if((NAV_Data_Full_p->GPS.gps_up_flag == E_GPS_IS_UPDATE) && ((NAV_Data_Full_p->GPS.NO_RTK_heading_flag) || (NAV_Data_Full_p->GPS.rtkStatus == E_GPS_RTK_FIXED)))
						{
						 NAV_Data_Full_p->KF.measure_flag_head	=	E_KALMAN_MEASURE_HEADING_YES;
						}
	  }
		 //********�������**->******
		if(NAV_Data_Full_p->UseCase==E_USE_CASE_In_Vehicle)
		{
			//********************
			 if(NAV_Data_Full_p->ODS.ods_flag==RETURN_SUCESS)//*****������***
			 {
						if(NAV_Data_Full_p->ODS.ods_caculat_flag ==RETURN_SUCESS)
						{
							NAV_Data_Full_p->KF.measure_flag_Wheel=RETURN_SUCESS;
							NAV_Data_Full_p->KF.measure_flag_NHC	=RETURN_SUCESS;
						}
						if(NAV_Data_Full_p->ODS.ods_normal_flag==RETURN_FAIL)
						{
							NAV_Data_Full_p->KF.measure_flag_Wheel=RETURN_FAIL;//*******�ų��쳣����********
						}
			 }
			 else//*******������****
			 {				
				 if (NAV_Data_Full_p->GPS.gps_up_flag == E_GPS_IS_UPDATE)
				 {
					 NAV_Data_Full_p->KF.measure_flag_NHC = RETURN_SUCESS;
					 NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms = NAV_Data_Full_p->GPS.gpssecond;
				 }
				 else
				 {
					 if ((NAV_Data_Full_p->GPS.gpssecond - NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms) >= SOURCE_UP_dTim_ms)
					 {
						 NAV_Data_Full_p->KF.measure_flag_NHC = RETURN_SUCESS;
						 NAV_Data_Full_p->Car_SOURCE_UP_Cur_ms = NAV_Data_Full_p->GPS.gpssecond;
					 }
				 }		 
			 }
 	}
	 //********�������**<-*****
	//*******ZUPT****
	if(NAV_Data_Full_p->ZUPT_flag==RETURN_SUCESS)
	{
		NAV_Data_Full_p->KF.measure_flag_ZUPT	=RETURN_SUCESS;
	}
	else
	{
		NAV_Data_Full_p->KF.measure_flag_ZUPT	=RETURN_FAIL;
	}
	//************all measurement******
	//******���ڵ���*****
	//NAV_Data_Full_p->KF.measure_flag_ZUPT = RETURN_FAIL;
	//NAV_Data_Full_p->KF.measure_flag_Wheel = RETURN_FAIL;//*****����****
	//NAV_Data_Full_p->KF.measure_flag_NHC = RETURN_FAIL;//****����****

	//NAV_Data_Full_p->KF.measure_flag_head = E_KALMAN_MEASURE_HEADING_NO;
	//NAV_Data_Full_p->KF.measure_flag_vn = E_KALMAN_MEASURE_VEL_NO;
	//NAV_Data_Full_p->KF.measure_flag_pos = E_KALMAN_MEASURE_POS_NO;

	if(  NAV_Data_Full_p->KF.measure_flag_ZUPT
	   ||NAV_Data_Full_p->KF.measure_flag_NHC
	   ||NAV_Data_Full_p->KF.measure_flag_head
	   ||NAV_Data_Full_p->KF.measure_flag_pos
	   ||NAV_Data_Full_p->KF.measure_flag_vn
	   ||NAV_Data_Full_p->KF.measure_flag_Wheel
	  )
		{
			 NAV_Data_Full_p->KF.measure_flag = E_KALMAN_MEASURE_UPDATE_HAVE;//E_KALMAN_MEASURE_UPDATE_NONE;
		}
	//***********************20240704******Guolong Zhang*******<-****
}
void CalDiffPosVelINS2RTK(_NAV_Data_Full_t *pNAV_Data_Full,double *pdeltaPosVel)
{
#if 0
	unsigned char i = 0;
	double pos[3] = {0};
	double pos_rtk[3]={0};
	double vn[3] = {0};
	double r_rtk[3]={0};
	double r_ins[3]={0};
	double r_diff[3]={0};
	double VectVel[3]={0};
	double VectPos[3]={0};
	
	for(i = 0;i<3;i++)
	{
		pos[i] = pNAV_Data_Full->SINS.pos[i];
		vn[i] =  pNAV_Data_Full->SINS.vn[i];
	}
	
	{
		VectVel[0]= vn[0]-pNAV_Data_Full->GPS.ve;
		VectVel[1]= vn[1]-pNAV_Data_Full->GPS.vn;
		VectVel[2]= vn[2]-pNAV_Data_Full->GPS.vu;
	}

	//rtk��λ��
	{
		pos_rtk[0]=pNAV_Data_Full->GPS.Lat* DEG2RAD;
		pos_rtk[1]=pNAV_Data_Full->GPS.Lon* DEG2RAD;
		pos_rtk[2]=pNAV_Data_Full->GPS.Altitude;
	}
	
	{
		pos2ecef(pos,r_ins);
		pos2ecef(pos_rtk,r_rtk);	
		for(i = 0;i<3;i++)
		{
			r_diff[i]=r_ins[i]-r_rtk[i];
		}
		ecef2enu(pos_rtk,r_diff,VectPos);
	}
	//ˮƽ�ٶ����
	pdeltaPosVel[1]=norm(VectVel,2);
	//ˮƽλ�����
	pdeltaPosVel[0]=norm(VectPos,2);
	//inav_log(INAVMD(LOG_DEBUG),"pdeltaPosVel=%f,%f",VectPos[0],pdeltaPosVel[1]);
#endif	
}
int CheckStandardCompleteStatus(_NAV_Data_Full_t *pNAV_Data_Full)
{
	//double vb_sins[3]={0};
	//double deltaPosVel[2]={0};
	//matmul("TN", 3, 1, 3, 1.0, pNAV_Data_Full->SINS.Cb2n, pNAV_Data_Full->SINS.vn, 0.0, vb_sins);

	//����rtk������ϵ���λ���ٶȲ�
//	CalDiffPosVelINS2RTK(pNAV_Data_Full,deltaPosVel);
//	//���㲹���˱ۺ�ˮƽ���
//	deltaPosVel[0]=deltaPosVel[0]-norm(pNAV_Data_Full->Param.gnssArmLength,2);
#if 0//�������P��������
	if (
		sqrt(pNAV_Data_Full->KF.Pxk[2 + NA * 2]) < (0.1 * DEG2RAD)//0.1*DEG2RAD //heading
		&& sqrt(pNAV_Data_Full->KF.Pxk[9 + NA * 9]) < (0.01 * DEG2RAD) //0.01*DEG2RAD //wbx
		&& sqrt(pNAV_Data_Full->KF.Pxk[10 + NA * 10]) < (0.01 * DEG2RAD) //0.01*DEG2RAD //wby
		&& sqrt(pNAV_Data_Full->KF.Pxk[11 + NA * 11]) < (0.01 * DEG2RAD) //0.01*DEG2RAD //wbz
		&& sqrt(pNAV_Data_Full->KF.Pxk[12 + NA * 12]) < (1.2 * MG)//1.2*MG //dbx
		&& sqrt(pNAV_Data_Full->KF.Pxk[13 + NA * 13]) < (1.2 * MG)//1.2*MG //dby
		&& sqrt(pNAV_Data_Full->KF.Pxk[14 + NA * 14]) < (0.5 * MG)//0.5*MG //dbz    
		)
#endif	

#if 0//�õ�460�ܳ�����		
		if (
			sqrt(pNAV_Data_Full->KF.Pxk[2 + NA * 2]) < (0.04 * DEG2RAD)//0.1*DEG2RAD //heading
			&& sqrt(pNAV_Data_Full->KF.Pxk[9 + NA * 9]) < (0.0003 * DEG2RAD) //0.01*DEG2RAD //wbx
			&& sqrt(pNAV_Data_Full->KF.Pxk[10 + NA * 10]) < (0.0003 * DEG2RAD) //0.01*DEG2RAD //wby
			&& sqrt(pNAV_Data_Full->KF.Pxk[11 + NA * 11]) < (0.0003 * DEG2RAD) //0.01*DEG2RAD //wbz
			&& sqrt(pNAV_Data_Full->KF.Pxk[12 + NA * 12]) < (0.1 * MG)//1.2*MG //dbx
			&& sqrt(pNAV_Data_Full->KF.Pxk[13 + NA * 13]) < (0.1 * MG)//1.2*MG //dby
			&& sqrt(pNAV_Data_Full->KF.Pxk[14 + NA * 14]) < (0.01 * MG)//0.5*MG //dbz    
			//����Լ��
			&& sqrt(pNAV_Data_Full->KF.Pxk[0 + NA * 0]) < (0.02 * DEG2RAD)//pitch
			&& sqrt(pNAV_Data_Full->KF.Pxk[1 + NA * 1]) < (0.02 * DEG2RAD)//roll
			&& sqrt(pNAV_Data_Full->KF.Pxk[3 + NA * 3]) < (0.01)//ve
			&& sqrt(pNAV_Data_Full->KF.Pxk[4 + NA * 4]) < (0.01)//vn
			&& sqrt(pNAV_Data_Full->KF.Pxk[5 + NA * 5]) < (0.01)//vu
			&& sqrt(pNAV_Data_Full->KF.Pxk[6 + NA * 6]) < (0.02 * RE_WGS84)//����λ��
			&& sqrt(pNAV_Data_Full->KF.Pxk[7 + NA * 7]) < (0.02 * RE_WGS84)//����λ��
			&& sqrt(pNAV_Data_Full->KF.Pxk[8 + NA * 8]) < (0.02)//����λ��
			&& sqrt(vb_sins[0] * vb_sins[0] + vb_sins[1] * vb_sins[1] + vb_sins[2] * vb_sins[2]) > 6.0
			&& deltaPosVel[0] < 0.18//��ϵ�����������0.06m
			&& deltaPosVel[1] < 0.6//�ٶ���������0.2m/s
			)
#endif

#if 0
			if (//2023-6-12�ܳ�����  
				sqrt(pNAV_Data_Full->KF.Pxk[2 + NA * 2]) < (0.08 * DEG2RAD)//0.1*DEG2RAD //heading
				&& sqrt(pNAV_Data_Full->KF.Pxk[9 + NA * 9]) < (0.0005 * DEG2RAD) //0.01*DEG2RAD //wbx
				&& sqrt(pNAV_Data_Full->KF.Pxk[10 + NA * 10]) < (0.0005 * DEG2RAD) //0.01*DEG2RAD //wby
				&& sqrt(pNAV_Data_Full->KF.Pxk[11 + NA * 11]) < (0.0005 * DEG2RAD) //0.01*DEG2RAD //wbz
				&& sqrt(pNAV_Data_Full->KF.Pxk[12 + NA * 12]) < (0.1 * MG)//1.2*MG //dbx
				&& sqrt(pNAV_Data_Full->KF.Pxk[13 + NA * 13]) < (0.1 * MG)//1.2*MG //dby
				&& sqrt(pNAV_Data_Full->KF.Pxk[14 + NA * 14]) < (0.02 * MG)//0.5*MG //dbz    
				//����Լ��
				&& sqrt(pNAV_Data_Full->KF.Pxk[0 + NA * 0]) < (0.03 * DEG2RAD)//pitch
				&& sqrt(pNAV_Data_Full->KF.Pxk[1 + NA * 1]) < (0.03 * DEG2RAD)//roll
				&& sqrt(pNAV_Data_Full->KF.Pxk[3 + NA * 3]) < (0.015)//ve
				&& sqrt(pNAV_Data_Full->KF.Pxk[4 + NA * 4]) < (0.015)//vn
				&& sqrt(pNAV_Data_Full->KF.Pxk[5 + NA * 5]) < (0.015)//vu
				&& sqrt(pNAV_Data_Full->KF.Pxk[6 + NA * 6]) < (0.02 * RE_WGS84)//����λ��
				&& sqrt(pNAV_Data_Full->KF.Pxk[7 + NA * 7]) < (0.02 * RE_WGS84)//����λ��
				&& sqrt(pNAV_Data_Full->KF.Pxk[8 + NA * 8]) < (0.02)//����λ��
				&& sqrt(vb_sins[0] * vb_sins[0] + vb_sins[1] * vb_sins[1] + vb_sins[2] * vb_sins[2]) > 6.0
				&& deltaPosVel[0] < 0.18//��ϵ�����������0.06m
				&& deltaPosVel[1] < 0.6//�ٶ���������0.2m/s
				//����ǲ���Լ��
				&& fabs(pNAV_Data_Full->Param.gnssAtt_from_vehicle2[2]) < 0.4//����ǲ�������
				)
#endif	

	
				//		for (int j = 1; j<SINS_BUFFER_SIZE;j++)
				//		{
				//			if(fabs(pNAV_Data_Full->SINS_buffer[j].att[2] - pNAV_Data_Full->SINS_buffer[j-1].att[2]) > 1 *DEG2RAD)
				//				return RETURN_FAIL;
				//		}


				//**********�о�1************20240921****Guolong Zhang*******//�����ܳ����ݹ���
				if(
					    sqrt(pNAV_Data_Full->KF.Pxk[2 + NA * 2]) < (1.0 * DEG2RAD) //�궨ʱ�䳬��10����//0.1*DEG2RAD //heading
						&& sqrt(pNAV_Data_Full->KF.Pxk[9 + NA * 9]) < (0.03 * DEG2RAD) //0.01*DEG2RAD //wbx
						&& sqrt(pNAV_Data_Full->KF.Pxk[10 + NA * 10]) < (0.03 * DEG2RAD) //0.01*DEG2RAD //wby
						&& sqrt(pNAV_Data_Full->KF.Pxk[11 + NA * 11]) < (0.035 * DEG2RAD) //0.01*DEG2RAD //wbz
						&& sqrt(pNAV_Data_Full->KF.Pxk[12 + NA * 12]) < (1.0 * MG)//1.2*MG //dbx
						&& sqrt(pNAV_Data_Full->KF.Pxk[13 + NA * 13]) < (1.0 * MG)//1.2*MG //dby
						&& sqrt(pNAV_Data_Full->KF.Pxk[14 + NA * 14]) < (0.8 * MG)//0.5*MG //dbz	 
						//����Լ��
				//		&& quake_count < 0
						&& sqrt(pNAV_Data_Full->KF.Pxk[0 + NA * 0]) < (0.4 * DEG2RAD)//pitch
						&& sqrt(pNAV_Data_Full->KF.Pxk[1 + NA * 1]) < (0.4 * DEG2RAD)//roll
						//&& sqrt(pNAV_Data_Full->KF.Pxk[3 + NA * 3]) < (0.35)//ve
						//&& sqrt(pNAV_Data_Full->KF.Pxk[4 + NA * 4]) < (0.35)//vn
						//&& sqrt(pNAV_Data_Full->KF.Pxk[5 + NA * 5]) < (0.35)//vu
						//&& sqrt(pNAV_Data_Full->KF.Pxk[6+NA*6])<(0.02*RE_WGS84)//����λ��
						//&& sqrt(pNAV_Data_Full->KF.Pxk[7+NA*7])<(0.02*RE_WGS84)//����λ��
						//&& sqrt(pNAV_Data_Full->KF.Pxk[8+NA*8])<(0.02)//����λ��
						//&& sqrt(vb_sins[0] * vb_sins[0] + vb_sins[1] * vb_sins[1] + vb_sins[2] * vb_sins[2]) > 0.5
						//&& norm(pNAV_Data_Full->SINS.vn, 3) > 0.5
					    //&& sqrt(pNAV_Data_Full->GPS.ve * pNAV_Data_Full->GPS.ve + pNAV_Data_Full->GPS.vn * pNAV_Data_Full->GPS.vn) > 0.5
					    && sqrt(pNAV_Data_Full->SubKF.P_b2gps)<(0.15* DEG2RAD)//****���˲���2******0.06*********
					    && pNAV_Data_Full->Subkf2_Cnt !=0   //�궨�㷨������
					    //&& pNAV_Data_Full->GPS.Position_Status != E_GPS_POS_INVALID//GPS��Ч�����ڵ�������					    
					    //&& pNAV_Data_Full->GPS.pdop <4.0
//			&& deltaPosVel[0]<0.18//��ϵ�����������0.06m
//			&& deltaPosVel[1]< 0.6//�ٶ���������0.2m/s
			//����ǲ���Լ��
			//&& fabs(pNAV_Data_Full->Param.gnssAtt_from_vehicle2[2])<0.4//����ǲ�������
		   )
		 {
		   //*******�ٴ��Ż�********20241015**********Guolong Zhang****************************************************
					//*****�о�2*********
					if (pNAV_Data_Full->Subkf2_Cnt == 1)//subkf2�����ڵ�һ�����ݽ��г�ʼ��
					{
						bias_acc_min[0] = pNAV_Data_Full->SINS.db[0];
						bias_acc_max[0] = pNAV_Data_Full->SINS.db[0];
						bias_acc_min[1] = pNAV_Data_Full->SINS.db[1];
						bias_acc_max[1] = pNAV_Data_Full->SINS.db[1];
						bias_acc_min[2] = pNAV_Data_Full->SINS.db[2];
						bias_acc_max[2] = pNAV_Data_Full->SINS.db[2];

						bias_wz_min[0] = pNAV_Data_Full->SINS.eb[0];
						bias_wz_max[0] = pNAV_Data_Full->SINS.eb[0];
						bias_wz_min[1] = pNAV_Data_Full->SINS.eb[1];
						bias_wz_max[1] = pNAV_Data_Full->SINS.eb[1];
						bias_wz_min[2] = pNAV_Data_Full->SINS.eb[2];
						bias_wz_max[2] = pNAV_Data_Full->SINS.eb[2];
						b2gps_min = pNAV_Data_Full->SubKF.att_b2gps[2];
						b2gps_max = pNAV_Data_Full->SubKF.att_b2gps[2];
					}
					else
					{
						//��ȡ�����ڵ���ֵ*******
						if (pNAV_Data_Full->SINS.db[0] < bias_acc_min[0])//dbx
						{
							bias_acc_min[0] = pNAV_Data_Full->SINS.db[0];
						}
						else if (pNAV_Data_Full->SINS.db[0] > bias_acc_max[0])
						{
							bias_acc_max[0] = pNAV_Data_Full->SINS.db[0];
						}
						if (pNAV_Data_Full->SINS.db[1] < bias_acc_min[1])//dby
						{
							bias_acc_min[1] = pNAV_Data_Full->SINS.db[1];
						}
						else if (pNAV_Data_Full->SINS.db[1] > bias_acc_max[1])
						{
							bias_acc_max[1] = pNAV_Data_Full->SINS.db[1];
						}
						if (pNAV_Data_Full->SINS.db[2] < bias_acc_min[2])//dbZ
						{
							bias_acc_min[2] = pNAV_Data_Full->SINS.db[2];
						}
						else if (pNAV_Data_Full->SINS.db[2] > bias_acc_max[2])
						{
							bias_acc_max[2] = pNAV_Data_Full->SINS.db[2];
						}

						if (pNAV_Data_Full->SINS.eb[0] < bias_wz_min[0])//ebx
						{
							bias_wz_min[0] = pNAV_Data_Full->SINS.eb[0];
						}
						else if (pNAV_Data_Full->SINS.eb[0] > bias_wz_max[0])
						{
							bias_wz_max[0] = pNAV_Data_Full->SINS.eb[0];
						}
						if (pNAV_Data_Full->SINS.eb[1] < bias_wz_min[1])//eby
						{
							bias_wz_min[1] = pNAV_Data_Full->SINS.eb[1];
						}
						else if (pNAV_Data_Full->SINS.eb[1] > bias_wz_max[1])
						{
							bias_wz_max[1] = pNAV_Data_Full->SINS.eb[1];
						}
						if (pNAV_Data_Full->SINS.eb[2] < bias_wz_min[2])//ebz
						{
							bias_wz_min[2] = pNAV_Data_Full->SINS.eb[2];
						}
						else if (pNAV_Data_Full->SINS.eb[2] > bias_wz_max[2])
						{
							bias_wz_max[2] = pNAV_Data_Full->SINS.eb[2];
						}
						if (pNAV_Data_Full->SubKF.att_b2gps[2] < b2gps_min)//b2gps
						{
							b2gps_min = pNAV_Data_Full->SubKF.att_b2gps[2];
						}
						else if (pNAV_Data_Full->SubKF.att_b2gps[2] > b2gps_max)
						{
							b2gps_max = pNAV_Data_Full->SubKF.att_b2gps[2];
						}
					}
					//***************�оݴ��������������˶���OK******
					if (pNAV_Data_Full->Subkf2_Cnt >= KF_Stable_Size)
					{
						pNAV_Data_Full->Subkf2_Cnt = 0;
						if (   (bias_acc_max[0] - bias_acc_min[0]) < (1.0 * MG)
							&& (bias_acc_max[1] - bias_acc_min[1]) < (1.0 * MG)
							&& (bias_acc_max[2] - bias_acc_min[2]) < (0.8 * MG)
							&& (bias_wz_max[0] - bias_wz_min[0]) < (0.02 * DEG2RAD)
							&& (bias_wz_max[1] - bias_wz_min[1]) < (0.02 * DEG2RAD)
							&& (bias_wz_max[2] - bias_wz_min[2]) < (0.025 * DEG2RAD)
							&& (b2gps_max - b2gps_min) < (0.2 * DEG2RAD)//0.008
							)
						{
							pNAV_Data_Full->KF_CheckCnt++;
							if (pNAV_Data_Full->KF_CheckCnt == Check_OK_Cnt)//******�о�3****
							{
								return RETURN_SUCESS;
							}
							else
							{
								return RETURN_FAIL;
							}
						}
						else
						{
							return RETURN_FAIL;
						}
				 }
				else
				{
						return RETURN_FAIL;
				}
		}
		else
		{
				return RETURN_FAIL;
		}
			
}
void CalcCa2n(_NAV_Data_Full_t* pNAV_Data_Full,double *Ca2n)
{
	double att[3]={0};
	double qnb[4]={0};
	att[0]=pNAV_Data_Full->GPS.Pitch*DEG2RAD;
	att[1]=pNAV_Data_Full->GPS.Roll*DEG2RAD;
	att[2]=pNAV_Data_Full->GPS.Heading*DEG2RAD;
	att2qnb(att,qnb);
	Qnb2Cnb(qnb, Ca2n);
}
void CalcAngleAntAndIMU(_NAV_Data_Full_t* pNAV_Data_Full)
{
	double Ca2n[3*3]={0};
	double Ca2b[3*3]={0};
	double att[3]={0};
	//����˫�����뵼��ϵת������
	CalcCa2n(pNAV_Data_Full,Ca2n);
	//����Ca2b
	matmul("NN", 3, 3, 3, 1.0, pNAV_Data_Full->SINS.Cn2b, Ca2n, 1.0, Ca2b);
	Cnb2att(Ca2b,att);
	pNAV_Data_Full->difAng=att[2];
}

void TEST_UP(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	double att_m2b[3] = {0};
	double wheel_speed=0;
	unsigned char valid_wheelspeed=RETURN_SUCESS;
	double vb[3]={0};
	int i,k;
	double temp=0.0;
	
	//��װ�ǶȲ��������к����ʹ����������ϵ�ٶȼ���
	att_m2b[0] = NAV_Data_Full_p->Param.OD_Att_from_vehicle[0]*DEG2RAD;
	//att_m2b[0] = NAV_Data_Full_p->ODS.att_ods2_b_filte[0]*DEG2RAD;	
	att_m2b[1] = NAV_Data_Full_p->Param.OD_Att_from_vehicle[1]*DEG2RAD;
	att_m2b[2] = NAV_Data_Full_p->ODS.att_ods2_b_filte[2]*DEG2RAD;	

	//�������ټ�����������ǰ���ٶȣ���Ҫ���ٶȳ��Կ̶�����
	wheel_speed=WheelSpeedOptimize(NAV_Data_Full_p->ODS.WheelSpeed_Front_Left*KMPH2MPS,
			NAV_Data_Full_p->ODS.WheelSpeed_Front_Right*KMPH2MPS,
			NAV_Data_Full_p->ODS.WheelSpeed_Back_Left*KMPH2MPS,
			NAV_Data_Full_p->ODS.WheelSpeed_Back_Right*KMPH2MPS,
			NAV_Data_Full_p->ODS.scale_factor_filte,// scale_factor_filte;scale_factor
			&valid_wheelspeed);
				//�������
	if(NAV_Data_Full_p->ODS.Gear == E_ODS_GEAR_BACKWARD)
	{			
		wheel_speed=-wheel_speed;
	}
	//����ϵ��mϵ��̬����
	Cn2m[0] =               NAV_Data_Full_p->SINS.Cn2b[0] + (-att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[1] + ( att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[2];
	Cn2m[1] = ( att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[0] +               NAV_Data_Full_p->SINS.Cn2b[1] + (-att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[2];
	Cn2m[2] = (-att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[0] + ( att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[1] +               NAV_Data_Full_p->SINS.Cn2b[2];

	Cn2m[0+3] =               NAV_Data_Full_p->SINS.Cn2b[0+3] + (-att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[1+3] + ( att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[2+3];
	Cn2m[1+3] = ( att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[0+3] +               NAV_Data_Full_p->SINS.Cn2b[1+3] + (-att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[2+3];
	Cn2m[2+3] = (-att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[0+3] + ( att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[1+3] +               NAV_Data_Full_p->SINS.Cn2b[2+3];
	
	Cn2m[0+6] =               NAV_Data_Full_p->SINS.Cn2b[0+6] + (-att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[1+6] + ( att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[2+6];
	Cn2m[1+6] = ( att_m2b[2])*NAV_Data_Full_p->SINS.Cn2b[0+6] +               NAV_Data_Full_p->SINS.Cn2b[1+6] + (-att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[2+6];
	Cn2m[2+6] = (-att_m2b[1])*NAV_Data_Full_p->SINS.Cn2b[0+6] + ( att_m2b[0])*NAV_Data_Full_p->SINS.Cn2b[1+6] +               NAV_Data_Full_p->SINS.Cn2b[2+6];

	Mat_Tr(3,Cn2m,Cm2n);
	if(RETURN_SUCESS == valid_wheelspeed)
	{
		for(i = 0;i<3;i++)
		{
			//��������ϵ�ٶ�
			vb[0]=0.0;
			vb[1]=wheel_speed;
			vb[2]=0.0;
			temp = 0;
			for(k = 0;k<3;k++)
			{
				temp += Cm2n[i+k*3]*vb[k];
			}
			NAV_Data_Full_p->SINS.vn[i]=  temp;							
		}
	}
	
}
/******************************************************************************
*ԭ  �ͣ�KF_UP2(_NAV_Data_Full_t* NAV_Data_Full_p)
*��  �ܣ��������˲�������
*��  �룺 
*��  ������
*******************************************************************************/
void KF_UP2(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{ 
	//������ʱ����
	int i,j,k;
 double temp = 0.0;
	
	//double temp_M[2][3] = {0};
	//double temp_M_T[3][2] = {0};
//	static int second982_pre = 0;
	//double v_OB_IMU[3] = {0}; //����ת�������ǣ���˶�
//	if (E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source)
//		measure_count++;
#if 0	
	if(second982_pre != CombineData_p->gnssInfo.gpssecond982)
	{
		
		NAV_Data_Full_p->GPS.gps_up_flag = E_GPS_IS_UPDATE;
		
		time_delay = NAV_Data_Full_p->GPS.gpssecond-second982_pre;
		second982_pre = CombineData_p->gnssInfo.gpssecond982;	
		while (time_delay<0)
		{
			time_delay = time_delay+1000.0/SAMPLE_FREQ_GNSS;
			
		}
		//inav_log(INAVMD(LOG_ERR),"%d\n",time_delay);
		//g_KF_UP2_gps_delay_cunt =floor(time_delay/1000/NAV_Data_Full_p->SINS.ts);
	
	}
#endif	
#if 0
	if(is_gnss_update(NAV_Data_Full_p))  //RETURN_SUCESS==1
	{
//		g_KF_UP2_gps_delay_cunt = 0;
		//NAV_Data_Full_p->GPS.gpssecond_old = NAV_Data_Full_p->GPS.gpssecond;
		NAV_Data_Full_p->GPS.gps_up_flag = E_GPS_IS_UPDATE;
		//��GPS���£��Ժ����������
		{
#if 0		
			NAV_Data_Full_p->SINS.att[2] = CorrHeading(-NAV_Data_Full_p->GPS.trackTrue)*DEG2RAD;
			//����Cn2b;��Ԫ��
			att2qnb(NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.qnb);
			Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
			Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.Cn2b);
			//����������IMU�н�
			CalcAngleAntAndIMU(NAV_Data_Full_p);
#endif			
		}
		
	}
	else//RETURN_FAIL==0
	{
		NAV_Data_Full_p->GPS.gps_up_flag = E_GPS_NO_UPDATE;
	}
#endif
	
	
// if(NAV_Data_Full_p->GPS.gps_up_flag == E_GPS_IS_UPDATE)	
//	{
//		g_KF_UP2_gps_delay_cunt++;
//	}
//*********************************************************
   //���ƹߵ���Գ����̵İ�װ��	
	if(  (NAV_Data_Full_p->GPS.Position_Status	!= E_GPS_POS_INVALID)
	     &&(NAV_Data_Full_p->GPS.gps_up_flag == E_GPS_IS_UPDATE)
	     &&(NAV_Data_Full_p->GPS.headingStatus==E_GPS_RTK_FIXED)
		//&&(NAV_Data_Full_p ->Nav_Status== E_NAV_STATUS_SYSTEM_STANDARD)//���˲���ʹ�ã����ﲻ��
	 )
	{
		ODS_Angle_Estimation(NAV_Data_Full_p);
	}

//�ں�Դѡ���Լ��ж��Ƿ���в�������
			SetFunsionStatusAndMeasureUpdate(NAV_Data_Full_p);				
//�۲�������͹۲���������
			ObsCalAndObsNoiseSet(NAV_Data_Full_p);
			
	//**20240715***Guolong Zhang**->***************
	//����������˲��㷨������ʵ�����̣�ϵͳά��15ά���㷨�н����˷ֲ������������ά���£�
	//�Ѹ�ά�������������ת��Ϊ��ά�����㣬������ֱ�ӵ���ϵͳ�ķ�װ����ֱ�����㣬�Լ��ټ��㿪��
	switch(NAV_Data_Full_p->KF.step)
	{			
		case E_KALMAN_TIME_UPDATE_1: //ʱ����� 1~4
		{
			/*
			if(0)
			{				
				//�����ǰ�������ںϣ���������Ч���л����˶�ģ��
				if( (E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source) && (RETURN_FAIL == g_valid_wheelspeed) )
				{
					SetNavFunsionSource(E_FUNSION_MOTION);
				}
				if (E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source
					|| E_FUNSION_MOTION == NAV_Data_Full_p->KF.fusion_source)
				{
					Q_MAT_UP(NAV_Data_Full_p);
				}
				
			}	
			*/
#if 0
				//����״̬ת�ƾ���Fk	
				/*
				#if 0
				if(0 == combineData.Param.HP)
				{
					Update_Phi(&(NAV_Data_Full_p->KF.Fk[0]),NAV_Data_Full_p,6);	
				}
				else
				{
					Update_Phi_HP(&(NAV_Data_Full_p->KF.Fk[0]),NAV_Data_Full_p,6);
				}
				#endif
				*/
#endif    	  
#if 0			
				//���Ը߾�����һ��phi�������	
				Update_Phi(&(NAV_Data_Full_p->KF.Fk[0]),*NAV_Data_Full_p,6);	
				double tmpFk[NA*NA];
				double dtFk[NA*NA];
				Update_Phi_HP(tmpFk,NAV_Data_Full_p,6);
				matrixSum(tmpFk, NAV_Data_Full_p->KF.Fk, NA, NA, -1,dtFk);

				
				for(i = 0;i<NA;i++)//X_ = F*X
				{
		
					for(j = 0;j<1;j++)
					{
						temp = 0;
						for(k = 0;k<NA;k++)
						{
							temp += NAV_Data_Full_p->KF.Fk[i+k*NA]*NAV_Data_Full_p->KF.Xk[k];						
						}
						NAV_Data_Full_p->KF.Xk_[i] = temp;
					}

					
				}
#endif

		//Ԥ�����****//�۲������۲�����������׼��******
		{
	   Update_Phi_HP(&(NAV_Data_Full_p->KF.Fk[0]),NAV_Data_Full_p, KLMAN_FILTER_SETP_SUM);
				matmul("NN", NA, 1, NA, 1.0, NAV_Data_Full_p->KF.Fk, NAV_Data_Full_p->KF.Xk, 0.0, NAV_Data_Full_p->KF.Xk_);
				//F*P����15*15���зֿ����				
				for(i = 0;i<7;i++)//F*P 1~7��
				{
					for(j = 0;j<NA;j++)
					{
						temp = 0.0;
						for(k = 0;k<NA;k++)
						{
							temp += NAV_Data_Full_p->KF.Fk[i+k* NA]*NAV_Data_Full_p->KF.Pxk[k+j* NA];
						}
						FP[i+j*NA] = temp;
					}
				}
	
		}
		  NAV_Data_Full_p->KF.step = E_KALMAN_TIME_UPDATE_2;
		}
		break; 
		case E_KALMAN_TIME_UPDATE_2:
		{
			{			
				for(i = 7;i<NA;i++) //F*P 8~15��
				{
					for(j = 0;j<NA;j++)
					{
						temp = 0.0;
						for(k = 0;k<NA;k++)
						{
							temp += NAV_Data_Full_p->KF.Fk[i+k*NA]*NAV_Data_Full_p->KF.Pxk[k+j* NA];
						}
						FP[i+j*NA] = temp;
					}
				}
			}
			NAV_Data_Full_p->KF.step = E_KALMAN_TIME_UPDATE_3;	
		}
		break;
		case E_KALMAN_TIME_UPDATE_3:
		{
		
			//Ԥ�����
			{
				for(i = 0;i<8;i++) //FP*F' 1~8��
				{
					for(j = 0;j<NA;j++)
					{
						temp = 0.0;
						for(k = 0;k<NA;k++)
						{
							temp += FP[i+k*NA]*NAV_Data_Full_p->KF.Fk[j+k*NA];//********��������װ��********
						}
						FPF[i+j*NA] = temp;
					}
				}
			}
			NAV_Data_Full_p->KF.step = E_KALMAN_TIME_UPDATE_4;
			
		}
		break;
		case E_KALMAN_TIME_UPDATE_4:
		{
			//Ԥ�����
			{
				for(i = 8;i<NA;i++)//FP*F' 9~15��
				{
					for(j = 0;j<NA;j++)
					{
						temp = 0.0;
						for(k = 0;k<NA;k++)
						{
							temp += FP[i+k*NA]*NAV_Data_Full_p->KF.Fk[j+k*NA];//********��������װ��********
						}
						FPF[i+j*NA] = temp;
					}
				}
                // P_k=F*P_k*F'+Qk					
				for(i = 0;i<NA;i++)    	
				{					
					for(j = 0;j<NA;j++)
					{
						NAV_Data_Full_p->KF.Pxk_[i+j*NA] = FPF[i+j*NA]+ NAV_Data_Full_p->KF.Qk[i+j*NA];								
					}
				}
				//Э�������Գƻ�,������Pk,k-1�ǶԳƾ��� 
				//symmetry(NAV_Data_Full_p->KF.Pxk_, NA, NAV_Data_Full_p->KF.Pxk_);
				if(0)
				{
					symmetry(NAV_Data_Full_p->KF.Pxk, NA, NAV_Data_Full_p->KF.Pxk_);
					for(i = 0;i<NA*NA;i++)
					{
						NAV_Data_Full_p->KF.Pxk[i] = NAV_Data_Full_p->KF.Pxk_[i];	
					}
				}
                //Pk Xk 				
				for(i = 0;i<NA;i++)    	
				{					
					for(j = 0;j<NA;j++)
					{	
						NAV_Data_Full_p->KF.Pxk[i+j*NA] = NAV_Data_Full_p->KF.Pxk_[i+j*NA];
					}
					NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i];
				}				
			}
			
			NAV_Data_Full_p->KF.step = E_KALMAN_MEASURE_UPDATE_1;	
			
		}
		break;
		case E_KALMAN_MEASURE_UPDATE_1://�������� 5~8
		{
			//�������� 
			//******ZUPT����**��vn��ͬʱ���£����ټ�����**
			if ((NAV_Data_Full_p->KF.measure_flag_ZUPT == RETURN_SUCESS)
				&& (NAV_Data_Full_p->KF.measure_flag_vn == E_KALMAN_MEASURE_VEL_NO))
			{
				//NAV_Data_Full_p->KF.measure_flag_ZUPT=RETURN_FAIL;
									  //����PH_T
				for (i = 0; i < NA; i++)
				{
					for (j = 0; j < 3; j++)
					{
						PH_T_ZUPT[i + NA * j] = NAV_Data_Full_p->KF.Pxk_[i + (3 + j) * NA];
					}
				}
				//���� Pz = HPH'+R
				for (i = 0; i < 3; i++)
				{
					for (j = 0; j < 3; j++)
					{
						if (i == j)
						{
							HPH_T_R_ZUPT[i + 3 * j] = PH_T_ZUPT[3 + i + NA * j] + R_ZUPT[i];
						}
						else
						{
							HPH_T_R_ZUPT[i + 3 * j] = PH_T_ZUPT[3 + i + NA * j];
						}
					}
				}
				//����inv_Pz
				{
					Mat3_Inv(HPH_T_R_ZUPT, HPH_T_R_Inc_ZUPT);// 					
				}
				//Kk  
				for (i = 0; i < NA; i++)
				{
					for (j = 0; j < 3; j++)
					{
						temp = 0;
						for (k = 0; k < 3; k++)
						{
							temp += NAV_Data_Full_p->KF.Pxk_[i + (k + 3) * NA] * HPH_T_R_Inc_ZUPT[k + 3 * j];
						}
						K_ZUPT[i + j * NA] = temp;
					}
				}
				//Xk = Xk_+ Kk*(zk-H*Xk_)
				for (i = 0; i < NA; i++)
				{
					temp = 0;
					for (k = 0; k < 3; k++)
					{
						temp += K_ZUPT[i + k * NA] * (Zk_ZUPT[k] - NAV_Data_Full_p->KF.Xk_[3 + k]);//***ok��******
					}
					NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i] + temp;
				}
				//Pk = Pk_-Kk*Hk*Pk_				
				for (i = 0; i < NA; i++)//Pk  
				{
					for (j = 0; j < NA; j++)
					{
						temp = 0;
						for (k = 0; k < 3; k++)
						{
							temp += K_ZUPT[i + k * NA] * NAV_Data_Full_p->KF.Pxk_[k + 3 + j * NA];
						}
						NAV_Data_Full_p->KF.Pxk[i + j * NA] = NAV_Data_Full_p->KF.Pxk_[i + j * NA] - temp;
					}
				}
				//Pk_ X_ 
				for (i = 0; i < NA; i++)
				{
					for (j = 0; j < NA; j++)
					{
						NAV_Data_Full_p->KF.Pxk_[i + j * NA] = NAV_Data_Full_p->KF.Pxk[i + j * NA];
					}
					NAV_Data_Full_p->KF.Xk_[i] = NAV_Data_Full_p->KF.Xk[i];
				}
			}
			//******ZUPT����****
				//�ٶȲ�������
			if (E_KALMAN_MEASURE_VEL_YES == NAV_Data_Full_p->KF.measure_flag_vn)
			{
				//�ں�RTK 
//					if(		(E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source)
//						//&&  (E_NAV_STATUS_IN_NAV== NAV_Data_Full_p->Nav_Status)
//						)
//					{
					//NAV_Data_Full_p->KF.measure_flag_vn=E_KALMAN_MEASURE_VEL_NO;
						//����PH_T
				for (i = 0; i < NA; i++)
				{
					for (j = 0; j < 3; j++)
					{
						PH_T_vn[i + NA * j] = NAV_Data_Full_p->KF.Pxk_[i + (3 + j) * NA];
					}
				}
				//���� Pz = HPH'+R
				for (i = 0; i < 3; i++)
				{
					for (j = 0; j < 3; j++)
					{
						if (i == j)
						{
							HPH_T_R_vn[i + 3 * j] = PH_T_vn[3 + i + NA * j] + R_vn[i];
						}
						else
						{
							HPH_T_R_vn[i + 3 * j] = PH_T_vn[3 + i + NA * j];
						}
					}
				}
				//����inv_Pz
				{
					Mat3_Inv(HPH_T_R_vn, HPH_T_R_Inc_vn);// 					
				}
				//Kk  
				for (i = 0; i < NA; i++)
				{
					for (j = 0; j < 3; j++)
					{
						temp = 0;
						for (k = 0; k < 3; k++)
						{
							temp += NAV_Data_Full_p->KF.Pxk_[i + (k + 3) * NA] * HPH_T_R_Inc_vn[k + 3 * j];
						}
						K_vn[i + j * NA] = temp;
					}
				}
				//Xk = Xk_+ Kk*(zk-H*Xk_)
				for (i = 0; i < NA; i++)
				{
					temp = 0;
					for (k = 0; k < 3; k++)
					{
						temp += K_vn[i + k * NA] * (Zk_vn[k] - NAV_Data_Full_p->KF.Xk_[3 + k]);
					}
					NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i] + temp;
				}
				//Pk = Pk_-Kk*Hk*Pk_				
				for (i = 0; i < NA; i++)//Pk  
				{
					for (j = 0; j < NA; j++)
					{
						temp = 0;
						for (k = 0; k < 3; k++)
						{
							temp += K_vn[i + k * NA] * NAV_Data_Full_p->KF.Pxk_[k + 3 + j * NA];
						}
						NAV_Data_Full_p->KF.Pxk[i + j * NA] = NAV_Data_Full_p->KF.Pxk_[i + j * NA] - temp;
					}
				}
				//Pk_ X_ 
				for (i = 0; i < NA; i++)
				{
					for (j = 0; j < NA; j++)
					{
						NAV_Data_Full_p->KF.Pxk_[i + j * NA] = NAV_Data_Full_p->KF.Pxk[i + j * NA];
					}
					NAV_Data_Full_p->KF.Xk_[i] = NAV_Data_Full_p->KF.Xk[i];
				}
				//}//�ں�RTK 
				//�ں�����������
			}
			NAV_Data_Full_p->KF.step = E_KALMAN_MEASURE_UPDATE_2;
		}
		break;
		case E_KALMAN_MEASURE_UPDATE_2://�������� 5~8
		{
//					else if( E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source 
//							//&& quake_count <=0
//							&& fabs(NAV_Data_Full_p->SINS.wnb[2] * RAD2DEG) < 100
//						)
					if((NAV_Data_Full_p->KF.measure_flag_Wheel==RETURN_SUCESS)
							&&((E_NAV_STATUS_IN_NAV == NAV_Data_Full_p->Nav_Status)||(E_NAV_STATUS_SYSTEM_STANDARD == NAV_Data_Full_p->Nav_Status))
						&& 1
						)//*********���ٸ���******
					{
						//NAV_Data_Full_p->KF.measure_flag_Wheel=RETURN_FAIL;
						//�ں�
  #if 0
						if(1)
						{
							//����PH_T(OK)
							{
								for(i = 0;i<NA;i++)
								{
									for(j = 0;j<3;j++)
									{
										temp = 0;
										for(k = 0;k<6;k++)
										{
											if(k<3)
											{
												//temp += 6.0*NAV_Data_Full_p->SINS.ts*NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Cn2m_vn_T[k+3*j];
												temp += NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Cn2m_vn_T[k+3*j];
											}
											else
											{
												temp += NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Cm2n[(k-3)+3*j];
											}
										}
										PH_T_m[i+NA*j] = temp;
									}
								}
							}
							//���� Pz = HPH'+R(�����)(OK)������
							{
								for(i = 0;i<3;i++)
								{
									for(j = 0;j<3;j++)
									{
										temp = 0;
										for(k = 0;k<6;k++)
										{
											if(k<3)
											{
												temp += Cn2m_vn[i+3*k]*PH_T_m[k+NA*j];
											}
											else
											{
												temp += Cn2m[i+3*(k-3)]*PH_T_m[k+NA*j];
											}
										}
										
										if(i == j)
										{
											HPH_T_R_m[i+3*j] = temp + R_m[i]; 
										}
										else
										{
											HPH_T_R_m[i+3*j] = temp; 
										}
									}
								}
							}
							//����inv_Pz(�����)(OK)
							{
								Mat3_Inv(HPH_T_R_m,HPH_T_R_Inc_m);// 													
							}						
							//Kk �������� ��(OK) 
							{
								//K  = PH_T*inv_Pz;
								for(i = 0;i<NA;i++)
								{
									for(j = 0;j<3;j++)
									{
										temp = 0;
										for(k = 0;k<3;k++)
										{
											temp += PH_T_m[i+NA*k]*HPH_T_R_Inc_m[k+3*j];
										}
										K_m[i+j*NA] = temp;
									}
								}
							}
							//Xk = Xk_+ Kk*(zk-H*Xk_) (�����) (OK)
							{
								//HX_ = Cn2m*Xk_
								double HX_m[3] = {0};
								for( i =0;i<3;i++)
								{
									temp = 0;
									for(k = 0;k<6;k++)
									{
										if(k<3)
										{
											temp += Cn2m_vn[i+k*3]*NAV_Data_Full_p->KF.Xk_[k];
										}
										else
										{
											temp += Cn2m[i+(k-3)*3]*NAV_Data_Full_p->KF.Xk_[k];
										}
									}
									HX_m[i] = temp;
								}
								//Xk = Xk_+ Kk*(zk-HX_) 
								for(i = 0;i<NA;i++)
								{
									temp = 0;
									for(k = 0;k<3;k++)
									{
										temp += K_m[i+k*NA]*(Zk_m[k]-HX_m[k]);
									}
									NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i] + temp;
								}
							}
							//Pk = Pk_-Kk*Hk*Pk_(�����)(OK)
							{						
								//HP 
								double HP_m[3*NA] = {0};
								for(i= 0;i<3;i++)
								{
									for(j = 0;j<NA;j++)
									{
										temp = 0;
										for(k = 0;k<6;k++)
										{
											if(k<3)
											{
												temp += Cn2m_vn[i+k*3]*NAV_Data_Full_p->KF.Pxk_[k+j*NA];
											}
											else
											{
												temp += Cn2m[i+(k-3)*3]*NAV_Data_Full_p->KF.Pxk_[k+j*NA];
											}
										}
										HP_m[i+3*j] = temp;
									}
								}
								//Pk = Pk_-Kk*HP    OK
								for(i = 0;i<NA;i++)//Pk   
								{
									for(j = 0;j<NA;j++)
									{
										temp = 0;
										for(k = 0;k<3;k++)
										{
											temp += K_m[i+k*NA]*HP_m[k+3*j];
										}
										NAV_Data_Full_p->KF.Pxk[i+j*NA] = NAV_Data_Full_p->KF.Pxk_[i+j*NA] - temp;							
									}
								}
							}
							//Pk_ X_ 
							{
								for(i = 0;i<NA;i++)
								{
									for(j = 0;j<NA;j++)
									{	
										NAV_Data_Full_p->KF.Pxk_[i+j*NA] = NAV_Data_Full_p->KF.Pxk[i+j*NA];
									}
									NAV_Data_Full_p->KF.Xk_[i] = NAV_Data_Full_p->KF.Xk[i];
								}
							}							
						}
  #endif

						if(1)
						{
							//����PH_T(OK)
							{
								for(i = 0;i<NA;i++)
								{
										temp = 0;
										for(k = 0;k<6;k++)
										{
											if(k<3)
											{
												//temp += 6.0*NAV_Data_Full_p->SINS.ts*NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Cn2m_vn_T[k+3*j];
												temp += NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Cn2m_vn_T[k+3*1];
											}
											else
											{
												temp += NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Cm2n[(k-3)+3*1];//Cn2m��ת��
											}
										}
										PH_T_m_s[i] = temp;
								}
							}
							//���� Pz = HPH'+R(�����)(OK)������
							{
										temp = 0;
										for(k = 0;k<6;k++)
										{
											if(k<3)
											{
												temp += Cn2m_vn[1+3*k]*PH_T_m_s[k];
											}
											else
											{
												temp += Cn2m[1+3*(k-3)]*PH_T_m_s[k];
											}
										}
								HPH_T_R_m_s = temp + R_m_s;
							}
							//����inv_Pz(�����)(OK)
							{
								//Mat3_Inv(HPH_T_R_m,HPH_T_R_Inc_m);// 		
								HPH_T_R_Inc_m_s=1.0/HPH_T_R_m_s;
							}						
							//Kk 
							{
								//K  = PH_T*inv_Pz;
								for(i = 0;i<NA;i++)
								{
									K_m_s[i] = PH_T_m_s[i]*HPH_T_R_Inc_m_s;
								}
							}
							//Xk = Xk_+ Kk*(zk-H*Xk_) (�����) (OK)
							{
								double HX_m = 0.0;
									temp = 0.0;
									for(k = 0;k<6;k++)
									{
										if(k<3)
										{
											temp += Cn2m_vn[1+k*3]*NAV_Data_Full_p->KF.Xk_[k];
										}
										else
										{
											temp += Cn2m[1+(k-3)*3]*NAV_Data_Full_p->KF.Xk_[k];
										}
									}
									HX_m = temp;
								//Xk = Xk_+ Kk*(zk-HX_) 
								for(i = 0;i<NA;i++)
								{
									 NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i] + K_m_s[i]*(Zk_m_s -HX_m);//*****Zk_m[1]**����������Ծ�ʹ��ԭ�������£�������****
								}
							}
							//Pk = Pk_-Kk*Hk*Pk_(�����)(OK)
							{						
								//HP 
								double HP_m[1*NA] = {0.0};
									for(j = 0;j<NA;j++)
									{
										temp = 0.0;
										for(k = 0;k<6;k++)
										{
											if(k<3)
											{
												temp += Cn2m_vn[1+k*3]*NAV_Data_Full_p->KF.Pxk_[k+j*NA];
											}
											else
											{
												temp += Cn2m[1+(k-3)*3]*NAV_Data_Full_p->KF.Pxk_[k+j*NA];
											}
										}
										HP_m[j] = temp;
								}
								//Pk = Pk_-Kk*HP    OK
								for(i = 0;i<NA;i++)//Pk   
								{
									for(j = 0;j<NA;j++)
									{
										NAV_Data_Full_p->KF.Pxk[i+j*NA] = NAV_Data_Full_p->KF.Pxk_[i+j*NA] - K_m_s[i]*HP_m[j];							
									}
								}
							}
							//Pk_ X_ 
							{
								for(i = 0;i<NA;i++)
								{
									for(j = 0;j<NA;j++)
									{	
										NAV_Data_Full_p->KF.Pxk_[i+j*NA] = NAV_Data_Full_p->KF.Pxk[i+j*NA];
									}
									NAV_Data_Full_p->KF.Xk_[i] = NAV_Data_Full_p->KF.Xk[i];
								}
							}							
						}
						
					}//�ں�����������
					//�ں��˶�Լ��ģ������
					//else if(E_FUNSION_MOTION == NAV_Data_Full_p->KF.fusion_source)
				 if((NAV_Data_Full_p->KF.measure_flag_NHC ==RETURN_SUCESS)
						&&((E_NAV_STATUS_IN_NAV == NAV_Data_Full_p->Nav_Status)||(E_NAV_STATUS_SYSTEM_STANDARD == NAV_Data_Full_p->Nav_Status))
					 &&1
					 )
					{
						//NAV_Data_Full_p->KF.measure_flag_NHC	=RETURN_FAIL;
						//�ں�
						if(1)
						{
							//����PH_T(OK)
							{
								for(i = 0;i<NA;i++)
								{
									for(j = 0;j<2;j++)
									{
										//**********
										temp = 0.0;
										for(k = 0;k<6;k++)
										{
											if(k<3)
											{
												//temp += 6.0*NAV_Data_Full_p->SINS.ts*NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Cn2m_vn_T[k+3*j];
												temp += NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Hk_NHC_Cn2m_vn_T[k+3*j];
											}
											else
											{
												temp += NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Hk_NHC_Cn2m_T[(k-3)+3*j];
											}
										}
										PH_T_m_xz[i+NA*j] = temp;
									}
								}
							}
							//���� Pz = HPH'+R(�����)(OK)������
							{	
								for(i = 0;i<2;i++)
								{
									for(j = 0;j<2;j++)
									{
										temp = 0.0;
										for(k = 0;k<6;k++)
										{
											if(k<3)
											{
												//temp += 6.0*NAV_Data_Full_p->SINS.ts*NAV_Data_Full_p->KF.Pxk_[i+NA*k]*Cn2m_vn_T[k+3*j];
												temp += Hk_NHC_Cn2m_vn[i+2*k]*PH_T_m_xz[k+NA*j];
											}
											else
											{
												temp += Hk_NHC_Cn2m[i+2*(k-3)]*PH_T_m_xz[k+NA*j];
											}
										}
										//*********
									if(i == j)
										{
											HPH_T_R_m_xz[i+2*j] = temp + R_m_xz[i]; 
										}
										else
										{
											HPH_T_R_m_xz[i+2*j] = temp;
										}	
									}
								}		
							}
							//����inv_Pz(�����)(OK)
							{
								Mat2_Inv(HPH_T_R_m_xz,HPH_T_R_Inc_m_xz);										
							}						
							//Kk 
							{
								//K  = PH_T*inv_Pz;
								for(i = 0;i<NA;i++)
								{
									for(j = 0;j<2;j++)
									{
										temp = 0.0;
										for(k = 0;k<2;k++)
										{
											temp += PH_T_m_xz[i+NA*k]*HPH_T_R_Inc_m_xz[k+2*j];
										}
										K_m_xz[i+j*NA] = temp;
									}
								}
							}
							//Xk = Xk_+ Kk*(zk-H*Xk_) (�����) (OK) 
							{	
                               //HX_ = H*Xk_								
								double HX_m_xz[2] = {0.0};							
								for(i = 0;i<2;i++)
								{
									temp = 0.0;
									for(k = 0;k<6;k++)
									{
										if(k<3)
										  temp += Hk_NHC_Cn2m_vn[i+2*k]*NAV_Data_Full_p->KF.Xk_[k];//***�ٴκ˶�һ��**��ok!!!��***
										else
											temp +=Hk_NHC_Cn2m[i+2*(k-3)]*NAV_Data_Full_p->KF.Xk_[k];
									}
									HX_m_xz[i] = temp;
								}
								//Xk = Xk_+ Kk*(zk-HX_) 
								for(i = 0;i<NA;i++)
								{
									temp = 0.0;
									for(k = 0;k<2;k++)
									{
										temp += K_m_xz[i+k*NA]*(Zk_m_xz[k]-HX_m_xz[k]);
									}
									NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i] + temp;
								}
							}
							//Pk = Pk_-Kk*Hk*Pk_(�����)(OK)
							{						
								//HP_
								for(i = 0;i<2;i++)
								{
									for(j = 0;j<NA;j++)
									{
										temp = 0.0;
										for(k = 0;k<6;k++)
										{
											if(k<3)
											  temp += Hk_NHC_Cn2m_vn[i+2*k]*NAV_Data_Full_p->KF.Pxk_[k+NA*j];//******ע���±�ĸı䣡��***
											else
												temp += Hk_NHC_Cn2m[i+2*(k-3)]*NAV_Data_Full_p->KF.Pxk_[k+NA*j];
										}
                                              HP_m_xz[i+2*j] = temp;										
									}
								}								
								//Pk = Pk_-Kk*HP_
								for(i = 0;i<NA;i++)
								{
									for(j = 0;j<NA;j++)
									{
										temp = 0.0;
										for(k = 0;k<2;k++)
										{
											temp += K_m_xz[i+NA*k]*HP_m_xz[k+2*j];
										}
										NAV_Data_Full_p->KF.Pxk[i+NA*j] = NAV_Data_Full_p->KF.Pxk_[i+NA*j] - temp;										
									}
								}
							}	
							//Pk_ X_ 
							{
								for(i = 0;i<NA;i++)
								{
									for(j = 0;j<NA;j++)
									{	
										NAV_Data_Full_p->KF.Pxk_[i+NA*j] = NAV_Data_Full_p->KF.Pxk[i+NA*j];
									}
									NAV_Data_Full_p->KF.Xk_[i] = NAV_Data_Full_p->KF.Xk[i];
								}
							}								
						}	
				 }//�ں��˶�Լ��ģ������								
			//}						
			NAV_Data_Full_p->KF.step = E_KALMAN_MEASURE_UPDATE_3;								 
		}
		break;
		case E_KALMAN_MEASURE_UPDATE_3://λ�ò������� 
		{
			//if (E_KALMAN_MEASURE_UPDATE_HAVE == NAV_Data_Full_p->KF.measure_flag)
			//{
				//λ�ò�������
				if (E_KALMAN_MEASURE_POS_YES == NAV_Data_Full_p->KF.measure_flag_pos)
				{
					//					if(E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source)//�ں�RTK 
					//					{
										//NAV_Data_Full_p->KF.measure_flag_pos=E_KALMAN_MEASURE_POS_NO;
											//����PH_T
					for (i = 0; i < NA; i++)
					{
						for (j = 0; j < 3; j++)
						{
							PH_T_pos[i + NA * j] = NAV_Data_Full_p->KF.Pxk_[i + (6 + j) * NA];
						}
					}
					//���� Pz = HPH'+R
					for (i = 0; i < 3; i++)
					{
						for (j = 0; j < 3; j++)
						{
							if (i == j)
							{
								HPH_T_R_pos[i + 3 * j] = PH_T_pos[6 + i + NA * j] + R_pos[i];
							}
							else
							{
								HPH_T_R_pos[i + 3 * j] = PH_T_pos[6 + i + NA * j];
							}
							HPH_T_R_Inc_pos[i + 3 * j] = HPH_T_R_pos[i + 3 * j];
						}
					}
					//����inv_Pz
					{
						Mat3_Inv(HPH_T_R_pos, HPH_T_R_Inc_pos);
						//							for(j = 0;j<3;j++)
						//							{
						//								PH_T_pos[i+NA*j] = NAV_Data_Full_p->KF.Pxk_[i+(6+j)*NA];							
						//							}							
						//                            matinv(HPH_T_R_Inc_pos,3);//���Ƿֽⷽʽ����						
					}
					//Kk  
					for (i = 0; i < NA; i++)
					{
						for (j = 0; j < 3; j++)
						{
							temp = 0;
							for (k = 0; k < 3; k++)
							{
								temp += NAV_Data_Full_p->KF.Pxk_[i + (6 + k) * NA] * HPH_T_R_Inc_pos[k + 3 * j];
							}
							K_pos[i + j * NA] = temp;
						}
					}
					//Xk = Xk_+ Kk*(zk-H*Xk_)
					for (i = 0; i < NA; i++)
					{
						temp = 0;
						for (k = 0; k < 3; k++)
						{
							temp += K_pos[i + k * NA] * (Zk_pos[k] - NAV_Data_Full_p->KF.Xk_[6 + k]);
						}
						NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i] + temp;
					}
					//Pk = Pk_-Kk*Hk*Pk_				
					for (i = 0; i < NA; i++)//Pk  
					{
						for (j = 0; j < NA; j++)
						{
							temp = 0;
							for (k = 0; k < 3; k++)
							{
								temp += K_pos[i + k * NA] * NAV_Data_Full_p->KF.Pxk_[6 + k + NA * j];
							}
							NAV_Data_Full_p->KF.Pxk[i + j * NA] = NAV_Data_Full_p->KF.Pxk_[i + NA * j] - temp;
						}
					}
					//Pk_ X_ 
					for (i = 0; i < NA; i++)
					{
						for (j = 0; j < NA; j++)
						{
							NAV_Data_Full_p->KF.Pxk_[i + j * NA] = NAV_Data_Full_p->KF.Pxk[i + j * NA];
						}
						NAV_Data_Full_p->KF.Xk_[i] = NAV_Data_Full_p->KF.Xk[i];
					}
					//}
#if 0
				else//�ں���������Դ
				{
					//��GPSʱ����̬����ͨ�˲�
					if (0)
					{
						//float att_acc[3] = {0};
						//att_acc[0] = atan2f( NAV_Data_Full_p->SINS.fb_ib[1],sqrt(NAV_Data_Full_p->SINS.fb_ib[0]*NAV_Data_Full_p->SINS.fb_ib[0]+NAV_Data_Full_p->SINS.fb_ib[2]*NAV_Data_Full_p->SINS.fb_ib[2]));
						//att_acc[1] = atan2f(-NAV_Data_Full_p->SINS.fb_ib[0],NAV_Data_Full_p->SINS.fb_ib[2]);//
						//att_acc[2] = NAV_Data_Full_p->SINS.att[2];

							double att_acc[3] = {0};
							att_acc[0] = atan2( NAV_Data_Full_p->SINS.fb_ib[1],sqrt(NAV_Data_Full_p->SINS.fb_ib[0]*NAV_Data_Full_p->SINS.fb_ib[0]+NAV_Data_Full_p->SINS.fb_ib[2]*NAV_Data_Full_p->SINS.fb_ib[2]));
							att_acc[1] = atan2(-NAV_Data_Full_p->SINS.fb_ib[0],NAV_Data_Full_p->SINS.fb_ib[2]);//
							att_acc[2] = NAV_Data_Full_p->SINS.att[2];
							

							//mode_fb =  sqrtf(NAV_Data_Full_p->SINS.fb_ib[0]*NAV_Data_Full_p->SINS.fb_ib[0]
							//			  + NAV_Data_Full_p->SINS.fb_ib[1]*NAV_Data_Full_p->SINS.fb_ib[1]
							//			  + NAV_Data_Full_p->SINS.fb_ib[2]*NAV_Data_Full_p->SINS.fb_ib[2])/9.8;

						    mode_fb =  sqrt(NAV_Data_Full_p->SINS.fb_ib[0]*NAV_Data_Full_p->SINS.fb_ib[0]
										  + NAV_Data_Full_p->SINS.fb_ib[1]*NAV_Data_Full_p->SINS.fb_ib[1]
									      + NAV_Data_Full_p->SINS.fb_ib[2]*NAV_Data_Full_p->SINS.fb_ib[2])/9.8;
							

							//����Ӧ���������˲�ʱ�����豸��Ȩ��
							{
								temp_weight = 1-3*ABS(1-mode_fb);
								
								 
								if(temp_weight>1)
								{
									temp_weight = 1;	
								}						
								else if(temp_weight<0)
								{
									temp_weight = 0;
								}			
							}
							k_filte = AHRS_DRIFT_PR_P*temp_weight;
							double AHRS_compevalue[3] = {0};
                            AHRS_compevalue[0] = WRAP_PI(att_acc[0]-NAV_Data_Full_p->SINS.att[0])*k_filte;
                            AHRS_compevalue[1] = WRAP_PI(att_acc[1]-NAV_Data_Full_p->SINS.att[1])*k_filte;
 							
							NAV_Data_Full_p->SINS.att[0] = WRAP_PI(NAV_Data_Full_p->SINS.att[0] + AHRS_compevalue[0]);
							NAV_Data_Full_p->SINS.att[1] = WRAP_PI(NAV_Data_Full_p->SINS.att[1] + AHRS_compevalue[1]);
							
							att2qnb(NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.qnb);	
							qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
							Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
						}										
					}		
#endif					
				}//λ�ò�������	
				NAV_Data_Full_p->KF.step = E_KALMAN_MEASURE_UPDATE_4;
		}
		break;
		case E_KALMAN_MEASURE_UPDATE_4://heading���������Լ����� (���һ���������)
		{
			//if (NAV_Data_Full_p->KF.measure_flag == E_KALMAN_MEASURE_UPDATE_HAVE)
			//{
				//�����������		
				if ((E_KALMAN_MEASURE_HEADING_YES == NAV_Data_Full_p->KF.measure_flag_head)				
									&&((E_NAV_STATUS_IN_NAV == NAV_Data_Full_p->Nav_Status)||(E_NAV_STATUS_SYSTEM_STANDARD == NAV_Data_Full_p->Nav_Status))
								&& 1
								//&& (fabs(NAV_Data_Full_p->SINS.wb_ib[2]*RAD2DEG)<MIN_TURN_GYRO_VALUE/5)
								//&& (NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED)
				   )
				{
					//NAV_Data_Full_p->KF.measure_flag_head=E_KALMAN_MEASURE_HEADING_NO;
#if 0
					//NAV_Data_Full_p->KF.RTK_Heading_OK = 0;
					//if(E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source)
					//{
						//����PH_T
					for (i = 0; i < NA; i++)
					{
						for (j = 0; j < 1; j++)
						{
							//�ο�H���󣬺����λ������̬������
							PH_T_heading[i + NA * j] = NAV_Data_Full_p->KF.Pxk_[i + (2 + j) * NA];
						}
					}
					//���� Pz = HPH'+R
					for (i = 0; i < 1; i++)
					{
						for (j = 0; j < 1; j++)
						{
							HPH_T_R_heading = PH_T_heading[2] + R_heading;
						}
					}
					//����inv_Pz
					{
						HPH_T_R_Inc_heading = 1 / HPH_T_R_heading;
					}
					//Kk  
					for (i = 0; i < NA; i++)
					{
						K_heading[i] = PH_T_heading[i] * HPH_T_R_Inc_heading;
					}
					//Xk = Xk_+ Kk*(zk-H*Xk_)
					for (i = 0; i < NA; i++)
					{
						//double deltaz=0;
						//double deltar=0;
						//deltaz=CorrHeading(NAV_Data_Full_p->KF.Xk_[2]*180.0/PI);
						//deltar=CorrHeading((Zk_heading-NAV_Data_Full_p->KF.Xk_[2])*180.0/PI);
						//temp = K_heading[i]*(deltar*PI/180.0);
						temp = K_heading[i] * (Zk_heading - NAV_Data_Full_p->KF.Xk_[2]);
						NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i] + temp;
					}
					//Pk = Pk_-Kk*Hk*Pk_				
					for (i = 0; i < NA; i++)//Pk  
					{
						for (j = 0; j < NA; j++)
						{
							temp = K_heading[i] * NAV_Data_Full_p->KF.Pxk_[2 + j * NA];
							NAV_Data_Full_p->KF.Pxk[i + j * NA] = NAV_Data_Full_p->KF.Pxk_[i + j * NA] - temp;
						}
					}
					//Pk_ X_ 
					for (i = 0; i < NA; i++)
					{
						for (j = 0; j < NA; j++)
						{
							NAV_Data_Full_p->KF.Pxk_[i + j * NA] = NAV_Data_Full_p->KF.Pxk[i + j * NA];
						}
						NAV_Data_Full_p->KF.Xk_[i] = NAV_Data_Full_p->KF.Xk[i];
					}
#endif
					//*****************20240723*********Guolong Zhang********->*****
					{
						//------------------------------------------
						double tmp = 0.0;
						double H_Pk[NA] = { 0 };
						//����PH_T
						for (i = 0; i < NA; i++)
						{
							tmp = 0.0;
							for (j = 0; j < 3; j++)
							{
								//�ο�H����
								tmp += NAV_Data_Full_p->KF.Pxk_[i + j * NA] * Hk_head[j];	//*******Hk_head��0���������µģ���*****					
							}
							PH_T_heading[i] = tmp;
						}
						//���� Pz = HPH'+R
						tmp = 0.0;
						for (i = 0; i < 3; i++)
						{
							tmp += Hk_head[i] * PH_T_heading[i];
						}
						HPH_T_R_heading = tmp + R_heading;
						//����inv_Pz
						{
							HPH_T_R_Inc_heading = 1.0 / HPH_T_R_heading;
						}
						//Kk  
						for (i = 0; i < NA; i++)
						{
							K_heading[i] = PH_T_heading[i] * HPH_T_R_Inc_heading;
						}
						//Xk = Xk_+ Kk*(zk-H*Xk_)
						tmp = 0.0;//H*Xk_
						for (i = 0; i < 3; i++)
						{
							tmp += Hk_head[i] * NAV_Data_Full_p->KF.Xk_[i];
						}
						for (i = 0; i < NA; i++)
						{
							NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i] + K_heading[i] * (Zk_heading - tmp);
						}
						//H*Pk_
						for (i = 0; i < NA; i++)
						{
							tmp = 0.0;
							for (j = 0; j < 3; j++)
							{
								tmp += Hk_head[j] * NAV_Data_Full_p->KF.Pxk_[j + i * NA];//*****�ٴκ˶�һ�£�***OK!!**
							}
							H_Pk[i] = tmp;
						}
						//Pk = Pk_-Kk*Hk*Pk_			
						tmp = 0.0;
						for (i = 0; i < NA; i++)//Pk  
						{
							for (j = 0; j < NA; j++)
							{
								tmp = K_heading[i] * H_Pk[j];
								NAV_Data_Full_p->KF.Pxk[i + j * NA] = NAV_Data_Full_p->KF.Pxk_[i + j * NA] - tmp;
							}
						}
						//Pk_ X_ 
						for (i = 0; i < NA; i++)
						{
							for (j = 0; j < NA; j++)
							{
								NAV_Data_Full_p->KF.Pxk_[i + j * NA] = NAV_Data_Full_p->KF.Pxk[i + j * NA];
							}
							NAV_Data_Full_p->KF.Xk_[i] = NAV_Data_Full_p->KF.Xk[i];
						}
						//------------------------------------------
					}
					//*****************20240723*********Guolong Zhang********<-*****
				//}
				}//����������� 
				//*******ZUPT heading������£���GNSSheading����********20241019����***Guolong Zhang********
					if (  E_KALMAN_MEASURE_HEADING_NO == NAV_Data_Full_p->KF.measure_flag_head
				&& NAV_Data_Full_p->KF.measure_flag_ZUPT
				&& NAV_Data_Full_p->ZUPTyaw_ST_Cnt >= ZUPT_DampingHeading_Size
					&&((E_NAV_STATUS_IN_NAV == NAV_Data_Full_p->Nav_Status)||(E_NAV_STATUS_SYSTEM_STANDARD == NAV_Data_Full_p->Nav_Status))
				)
  {
				{
					double tmp = 0.0;
					double H_Pk[NA] = { 0 };
					//����PH_T
					for (i = 0; i < NA; i++)
					{
						tmp = 0.0;
						for (j = 0; j < 3; j++)
						{
							//�ο�H����
							tmp += NAV_Data_Full_p->KF.Pxk_[i + j * NA] * ZUPT_Hk_head[j];	//*******Hk_head��0���������µģ���*****					
						}
						PH_T_heading[i] = tmp;
					}
					//���� Pz = HPH'+R
					tmp = 0.0;
					for (i = 0; i < 3; i++)
					{
						tmp += ZUPT_Hk_head[i] * PH_T_heading[i];
					}
					HPH_T_R_heading = tmp + R_ZUPT_Heading;
					//����inv_Pz
					{
						HPH_T_R_Inc_heading = 1.0 / HPH_T_R_heading;
					}
					//Kk  
					for (i = 0; i < NA; i++)
					{
						K_heading[i] = PH_T_heading[i] * HPH_T_R_Inc_heading;
					}
					//Xk = Xk_+ Kk*(zk-H*Xk_)
					tmp = 0.0;//H*Xk_
					for (i = 0; i < 3; i++)
					{
						tmp += ZUPT_Hk_head[i] * NAV_Data_Full_p->KF.Xk_[i];
					}
					for (i = 0; i < NA; i++)
					{
						NAV_Data_Full_p->KF.Xk[i] = NAV_Data_Full_p->KF.Xk_[i] + K_heading[i] * (Zk_ZUPT_Heading - tmp);
					}
					//H*Pk_
					for (i = 0; i < NA; i++)
					{
						tmp = 0.0;
						for (j = 0; j < 3; j++)
						{
							tmp += ZUPT_Hk_head[j] * NAV_Data_Full_p->KF.Pxk_[j + i * NA];//*****�ٴκ˶�һ�£�***OK!!**
						}
						H_Pk[i] = tmp;
					}
					//Pk = Pk_-Kk*Hk*Pk_			
					tmp = 0.0;
					for (i = 0; i < NA; i++)//Pk  
					{
						for (j = 0; j < NA; j++)
						{
							tmp = K_heading[i] * H_Pk[j];
							NAV_Data_Full_p->KF.Pxk[i + j * NA] = NAV_Data_Full_p->KF.Pxk_[i + j * NA] - tmp;
						}
					}
					//Pk_ X_ 
					for (i = 0; i < NA; i++)
					{
						for (j = 0; j < NA; j++)
						{
							NAV_Data_Full_p->KF.Pxk_[i + j * NA] = NAV_Data_Full_p->KF.Pxk[i + j * NA];
						}
						NAV_Data_Full_p->KF.Xk_[i] = NAV_Data_Full_p->KF.Xk[i];
					}
				}
  }
			 //*****other measure*********
			//״̬����			
			if(E_KALMAN_MEASURE_UPDATE_HAVE == NAV_Data_Full_p->KF.measure_flag)
			{
				//Pk�Գƻ� DW:Ϊ��KF_UP_Standard����Ϊif(1)
			  symmetry(NAV_Data_Full_p->KF.Pxk,NA, NAV_Data_Full_p->KF.Pxk);
		#if 0
				if(1)
				{
					symmetry(NAV_Data_Full_p->KF.Pxk,NA, NAV_Data_Full_p->KF.Pxk);
					for(i =0;i<NA;i++)
					{
						for(j = 0;j<i+1;j++)
						{
							temp = 0.5*(NAV_Data_Full_p->KF.Pxk[i+NA*j]+NAV_Data_Full_p->KF.Pxk[j+NA*i]);
							NAV_Data_Full_p->KF.Pxk[i+NA*j] = temp;
							NAV_Data_Full_p->KF.Pxk[j+NA*i] = temp;
						}
					}
				}
			#endif
				//Pk�޷�����ֹ������//
				if(0)
				{
					for(i = 0;i<NA;i++)
					{
						//�Ӽơ����ٶ�Э����
						if(i>=9)
						//if(i<9)
						{
							if(NAV_Data_Full_p->KF.Pxk[i+i*NA] < NAV_Data_Full_p->KF.P_min[i])
							{
#ifdef linux
								//inav_log(INAVMD(LOG_ERR),"Pk is too small, Pxk[%d]=%12.8e,min=%12.8e",i,NAV_Data_Full_p->KF.Pxk[i+i*NA], NAV_Data_Full_p->KF.P_min[i]);	
#endif							
     							NAV_Data_Full_p->KF.Pxk[i+i*NA] = NAV_Data_Full_p->KF.P_min[i];
							}
#if 1							
							//DW:Ϊ��KF_UP_Standard����ûע�͵�
							if(NAV_Data_Full_p->KF.Pxk[i+i*NA] > NAV_Data_Full_p->KF.P_max[i])
							{
	 							NAV_Data_Full_p->KF.Pxk[i+i*NA] = NAV_Data_Full_p->KF.P_max[i];
							}
#endif							
						}							
					}
				}										
				//debug_KF_Xk
				if(1)
				{
					for(i = 0;i<NA;i++)
					{
						debug_KF_Xk[i] = NAV_Data_Full_p->KF.Xk[i];
					}
				}//debug_KF_Xk				
				//״̬����								
			  KfFeedback(NAV_Data_Full_p, KLMAN_FILTER_SETP_SUM);	//״̬����				
		   NAV_Data_Full_p->KF.measure_flag = E_KALMAN_MEASURE_UPDATE_NONE;
		}

			//�ж��Ƿ�궨���
			if(E_NAV_STATUS_SYSTEM_STANDARD == NAV_Data_Full_p->Nav_Status)
			{
				if(RETURN_SUCESS == CheckStandardCompleteStatus(NAV_Data_Full_p))
				{
					//NAV_Data_Full_p->ODS.att_ods2_b_filte[2] = NAV_Data_Full_p->SINS.ods2gnssfilter - NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2];//*****
					SetNavStandardFlag(E_NAV_STANDARD_PROCCSSED);
					//�궨��Ϻ�,������д��flash
					NavStandardParm2Flash(NAV_Data_Full_p);
					//NAV_Data_Full.Nav_Standard_flag = E_NAV_STANDARD_PROCCSSED; //0:δ�궨��� 1���궨�� 2���궨���
				}
				else
				{
					SetNavStandardFlag(E_NAV_STANDARD_PROCCSSING);
					//NAV_Data_Full.Nav_Standard_flag = E_NAV_STANDARD_PROCCSSING;
				}
				
				if(sqrt(NAV_Data_Full.KF.Pxk[2+NA*2])  <1*DEG2RAD)
				{
					NAV_Data_Full.Param.test_flag[0] = 1;					
				}
				if(sqrt(NAV_Data_Full.KF.Pxk[9+NA*9])  <0.01*DEG2RAD)
				{
					NAV_Data_Full.Param.test_flag[1] = 1;					
				}
				if(sqrt(NAV_Data_Full.KF.Pxk[10+NA*10])<0.01*DEG2RAD)
				{
					NAV_Data_Full.Param.test_flag[2] = 1;					
				}
				if(sqrt(NAV_Data_Full.KF.Pxk[11+NA*11])<0.02*DEG2RAD)
				{
					NAV_Data_Full.Param.test_flag[3] = 1;					
				}
				if(NAV_Data_Full.KF.Pxk[12+NA*12]<1*MG)
				{
					NAV_Data_Full.Param.test_flag[4] = 1;					
				}
				if(NAV_Data_Full.KF.Pxk[13+NA*13]<1*MG)
				{
					NAV_Data_Full.Param.test_flag[5] = 1;					
				}
				if(NAV_Data_Full.KF.Pxk[14+NA*14]<1*MG)
				{
					NAV_Data_Full.Param.test_flag[6] = 1;					
				}
				if(++NAV_Data_Full_p->KF.cunt >= 5*60*2)
				{
					NAV_Data_Full_p->KF.cunt = 0;
				}
				
			}
			//���������Ѿ��������
			NAV_Data_Full_p->KF.measure_flag_ZUPT = RETURN_FAIL;
			NAV_Data_Full_p->KF.measure_flag_Wheel = RETURN_FAIL;//*********
			NAV_Data_Full_p->KF.measure_flag_NHC = RETURN_FAIL;//********
			NAV_Data_Full_p->KF.measure_flag_head = E_KALMAN_MEASURE_HEADING_NO;
			NAV_Data_Full_p->KF.measure_flag_vn = E_KALMAN_MEASURE_VEL_NO;
			NAV_Data_Full_p->KF.measure_flag_pos = E_KALMAN_MEASURE_POS_NO;
			//*************
			NAV_Data_Full_p->KF.step = E_KALMAN_TIME_UPDATE_1;
		}
		break;
		default :
		{
			NAV_Data_Full_p->KF.step = E_KALMAN_TIME_UPDATE_1;
		}
		break;		
	}		
}
#if 0
/******************************************************************************
*ԭ  �ͣ�void LeverarmTimeCorr(INSRESULT* ins,double *gpsVn,double *gpsPos,double heading,double* lever, double dt,double *Zk) //GPS���ݱ۸˲���
*��  �ܣ�GPS���ݱ۸˲���
*��  �룺 
*��  ������
*******************************************************************************/
static void LeverarmTimeCorr(double *Zk,_NAV_Data_Full_t* NAV_Data_Full_p,double heading,double* lever,double *gpsVn,double *gpsPos,double dt) //GPS���ݱ۸˲���
{
	int i, k;
   
	double CW_[3*3],CW[3*3];
	double Cnb[3*3];
	double MpvCnb[3* 3],MpvVn[3];
	double Cwlever[3],MpvCnblever[3];
	double vnL_[3],vnL[3];
	double posL_[3],posL[3];

	double att_ref[3] = {0,0,0};
	double q_ref[4] = {1,0,0,0};
	double q_temp[4] = {1,0,0,0};
	double dq[4] = {1,0,0,0};
	double d_att[3] = {0,0,0};
//	double Hk_k_1[NN];
//	Hk_k_1[0] = 0.0;
//	Hk_k_1[1] = sin(ins->att[1]) / cos(ins->att[0]); 
//	Hk_k_1[2] = cos(ins->att[1]) / cos(ins->att[0]);

	askew(NAV_Data_Full_p->SINS.wnb, CW_);  //[wnb X]
	Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, Cnb);
	matmul("NN", 3, 3, 3, 1.0, Cnb, CW_, 0.0, CW);
	matmul("NN", 3, 3, 3, 1.0, NAV_Data_Full_p->SINS.Mpv, Cnb, 0.0, MpvCnb);
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Mpv, NAV_Data_Full_p->SINS.vn, 0.0, MpvVn);

	matmul("NN", 3, 1, 3, 1.0, CW, lever, 0.0, Cwlever);  //wnb x lever
	matrixSum(NAV_Data_Full_p->SINS.vn, Cwlever, 3, 1, 1, vnL_); //sins�ٶȱ۸˲������ֵ
	matrixSum(vnL_, NAV_Data_Full_p->SINS.an, 3, 1, -dt, vnL);   //sins�ٶ���ʱ�������ֵ

	matmul("NN", 3, 1, 3, 1.0, MpvCnb, lever, 0.0, MpvCnblever);
	matrixSum(NAV_Data_Full_p->SINS.pos, MpvCnblever, 3, 1, 1, posL_);//sinsλ�ñ۸˲������ֵ
	matrixSum(posL_, MpvVn, 3, 1, -dt, posL);        //sinsλ����ʱ�������ֵ

#if 1
//	if (MOTION_STATUS_HEADING == g_STATIC_DETECTION.state)//��ǰû�и�ģʽ
	if(0)
	{
		for (i = 0;i < 6; i++)
		{
			if (i < 3)
			{
				Zk[i] = vnL[i] - gpsVn[i];
			}
			else
			{
				Zk[i] = posL[i - 3] - gpsPos[i - 3];
			}
		}
		//-------����Լ���в�Zk[6]--H���󹹽�--------------------------------------------------------------
		Zk[6]=NAV_Data_Full_p->SINS.att[2]-NAV_Data_Full_p->SINS.att_pre[2];
		//-----------------------------------------------------------------------------------
	}
#endif
	else
	{
		for (i = 0; i < 6; i++)
		{
			if (i < 3)
			{
//				Zk[i] = ins->vn[i] - gpsVn[i];
				Zk[i] = vnL[i] - gpsVn[i];
			}
			else if(i<6)
			{
				Zk[i] = posL[i - 3] - gpsPos[i - 3];
			}
			//else Zk[k++]=ins->att[2]-heading;
			//else Zk[k++]=0.0;
		}
		att_ref[0] = NAV_Data_Full_p->SINS.att[0];
		att_ref[1] = NAV_Data_Full_p->SINS.att[1];
		att_ref[2] = heading;
		att2qnb(att_ref,q_ref);
		qConj(NAV_Data_Full_p->SINS.qnb,q_temp);
		qnbmul(q_temp,q_ref,dq);
		qnb2att(dq,d_att);
	//  Zk[6]=ins->att[2]-heading;
	    Zk[6]= d_att[2];		
	}
}
#endif
/******************************************************************************
*ԭ  �ͣ�void LeverarmTimeCorr2(INSRESULT* ins,double *gpsVn,double *gpsPos,double heading,double* lever, double dt,double *Zk) //GPS���ݱ۸˲���
*��  �ܣ�GPS���ݱ۸˲���,δ����ȫ��buff����
*��  �룺 
*��  ������
*******************************************************************************/
//��������������GPSʱ�������SINS�����������ǰSINS���������SINS���
static void LeverarmTimeCorr2(double *Zk,_NAV_Data_Full_t* NAV_Data_Full_p,double heading,double* lever,double *gpsVn,double *gpsPos,double dt) //GPS���ݱ۸˲���
{
	int i;
	double tmp_pit=0.0;
	double CW_[3*3],CW[3*3];
	double Cnb[3*3];
	double MpvCnb[3* 3],MpvVn[3];
	double Cwlever[3],MpvCnblever[3];
	double vnL_[3],vnL[3];
	double posL_[3],posL[3];
/*
	double att_ref[3] = {0,0,0};
	double q_ref[4] = {1,0,0,0};
	double q_temp[4] = {1,0,0,0};
	double dq[4] = {1,0,0,0};
	double d_att[3] = {0,0,0};

	int delay_cunt = 0;
	int sins_cunt = 0;
	_SINS_BUFFER_t m_sins;
	double timediff=0;
	*/
//	unsigned char delay_cunt = 0;
//	double Hk_k_1[NN];
//	Hk_k_1[0] = 0.0;
//	Hk_k_1[1] = sin(ins->att[1]) / cos(ins->att[0]); 
//	Hk_k_1[2] = cos(ins->att[1]) / cos(ins->att[0]);

	//***********20240718 Guolong Zhang***************
		//*****proposed method**->***��ʱ����***
	  double wnb[3]={0};
		 double tpos,tvn,theading,dpos[3]={0,0,0},dvn[3]={0,0,0},dheading=0.0,t_outage;
			double GPSTs_cnt;//***GPS�������ڵ���������***
			GPSTs_cnt=2*1.0/SAMPLE_FREQ_GNSS;
			tpos =  dt;
			tvn =  dt;
			theading = dt;
			t_outage=fabs((NAV_Data_Full_p->GPS.gpssecond982-NAV_Data_Full_p->GPS.GPS_UP_Pre.gpssecond982_old)/1000.0);//***s*****

		//***********
		if (fabs(gpsVn[0] - NAV_Data_Full_p->SINS.vn[0]) < 1.0 && fabs(gpsVn[1] - NAV_Data_Full_p->SINS.vn[1]) < 1.0
			&& fabs(gpsVn[2] - NAV_Data_Full_p->SINS.vn[2]) < 1.0 && fabs(gpsPos[2] - NAV_Data_Full_p->SINS.pos[2]) < 25.0)//***���SPP*******
		{
			dvn[0] = NAV_Data_Full_p->SINS.an[0] * tvn;
			dvn[1] = NAV_Data_Full_p->SINS.an[1] * tvn;
			dvn[2] = NAV_Data_Full_p->SINS.an[2] * tvn;

			dpos[1] = NAV_Data_Full_p->SINS.vn[0] * tpos / NAV_Data_Full_p->EARTH.clRNh;
			dpos[0] = NAV_Data_Full_p->SINS.vn[1] * tpos / NAV_Data_Full_p->EARTH.RMh;
			dpos[2] = NAV_Data_Full_p->SINS.vn[2] * tpos;
		}
		//*******���򲹳�***
  if(NAV_Data_Full_p->GPS.headingStatus == E_GPS_RTK_FIXED)
		{
			if((t_outage<=GPSTs_cnt)
				&&(NAV_Data_Full_p->GPS.Position_Status==NAV_Data_Full_p->GPS.GPS_UP_Pre.Position_Status)
				&&((NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID_RTK)||(NAV_Data_Full_p->GPS.Position_Status == E_GPS_POS_VALID))
							)
					{
						dheading= CorrHeading_PI(heading-DEG2RAD*NAV_Data_Full_p->GPS.GPS_UP_Pre.Heading_cor)*theading/t_outage;//***rad**
					}

		}
			//
	  wnb[0]=NAV_Data_Full_p->IMU.gyro_use[0];//-NAV_Data_Full_p->SINS.eb[0];
		 wnb[1]=NAV_Data_Full_p->IMU.gyro_use[1];//-NAV_Data_Full_p->SINS.eb[1];
		 wnb[2]=NAV_Data_Full_p->IMU.gyro_use[2];//-NAV_Data_Full_p->SINS.eb[2];
		 askew(wnb, CW_);  //[wnb X]//���Գƾ�����ʽ
	  Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, Cnb);//Cb2n
	  matmul("NN", 3, 3, 3, 1.0, Cnb, CW_, 0.0, CW);
	  matmul("NN", 3, 3, 3, 1.0, NAV_Data_Full_p->SINS.Mpv, Cnb, 0.0, MpvCnb);
	  //matmul("NN", 3, 1, 3, 1.0, m_sins.Mpv, m_sins.vn, 0.0, MpvVn);
	  matmul("NN", 3, 1, 3, 1.0, CW, lever, 0.0, Cwlever);  //wnb x lever
	  matmul("NN", 3, 1, 3, 1.0, MpvCnb, lever, 0.0, MpvCnblever);
		 matrixSum(NAV_Data_Full_p->SINS.vn, Cwlever, 3, 1, 1, vnL); //sins�ٶȱ۸˲������ֵ//����win_n
		 matrixSum(NAV_Data_Full_p->SINS.pos, MpvCnblever, 3, 1, 1, posL);//sinsλ�ñ۸˲������ֵ RG=RI+DCL,λ�ø˱�û����
			for (i = 0; i < 6; i++)
			{
				if (i < 3)
				{
					Zk[i] = vnL[i] - (gpsVn[i]+dvn[i]) ;//*****vn,pos ,heading**Ϊ����******
				}
				else if(i<6)
				{
					Zk[i] = posL[i - 3] - (gpsPos[i - 3]+dpos[i - 3]);
				}
			}	
			//*******************
			Zk[6]=CorrHeading_PI(NAV_Data_Full_p->SINS.att[2] - CorrHeading_PI(CorrHeading_PI(heading+dheading)+NAV_Data_Full_p->SubKF.att_b2gps[2]));
			//
			tmp_pit=NAV_Data_Full_p->SINS.att[0];
			if(tmp_pit>TH_pit)
			{
				tmp_pit=TH_pit;
			}
			if(tmp_pit<-TH_pit)
			{
				tmp_pit=-TH_pit;
			}
			Hk_head[0]=-tan(tmp_pit)*sin(NAV_Data_Full_p->SINS.att[2]);
			Hk_head[1]=tan(tmp_pit)*cos(NAV_Data_Full_p->SINS.att[2]);
			Hk_head[2]=-1.0;
	//*****proposed method**<-**************
	
	#if 0
	
#if 0
	delay_cunt2 = fmod(dt,TS);
	if(delay_cunt2>(TS/2))
	{
		delay_cunt = (dt/TS)+1;
	}
	else
	{
		delay_cunt = dt/TS;
	}
#endif	
	delay_cunt=(int)round(dt/TS);

	//ϵͳ�궨ʱ��ʹ�õ�ǰsins����и˱۲���
	if(E_NAV_STATUS_SYSTEM_STANDARD == NAV_Data_Full_p->Nav_Status)
	//if(1)
	{
		//qnb
		for(i = 0;i<4;i++)
		{
			m_sins.qnb[i] = NAV_Data_Full_p->SINS.qnb[i];
		}
		//att��vn��pos
		for(i = 0;i<3;i++)
		{
			m_sins.att[i] = NAV_Data_Full_p->SINS.att[i];
			m_sins.vn[i] = NAV_Data_Full_p->SINS.vn[i];
			m_sins.pos[i] = NAV_Data_Full_p->SINS.pos[i];			
		}
		//wnb
		for(i = 0;i<3;i++)
		{
			m_sins.wnb[i] = NAV_Data_Full_p->SINS.wnb[i];			
		}  

		//an
		for(i = 0;i<3;i++)
		{
			m_sins.an[i] = NAV_Data_Full_p->SINS.an[i];			
		}
		
        //Mpv
		for(i = 0;i<9;i++)
		{
			m_sins.Mpv[i] = NAV_Data_Full_p->SINS.Mpv[i];
		}
		
		timediff = dt;
	}
	else if(E_NAV_STATUS_IN_NAV == NAV_Data_Full_p->Nav_Status)
	{
		//Ѱ�һ�������GPSʱ�������
		//��delay_cunt������󻺴棬ѡ��Head����һ�����ݣ�����������
		if(delay_cunt>=SINS_BUFFER_SIZE)
		{
			if(NAV_Data_Full_p->Head==(SINS_BUFFER_SIZE-1))
			{
				memcpy(&m_sins,&NAV_Data_Full_p->SINS_buffer[0],sizeof(_SINS_BUFFER_t));
			}
			else
			{
				memcpy(&m_sins,&NAV_Data_Full_p->SINS_buffer[NAV_Data_Full_p->Head+1],sizeof(_SINS_BUFFER_t));
			}
			timediff=dt-((double)SINS_BUFFER_SIZE-1)*TS;
		}
		else
		{
			sins_cunt = NAV_Data_Full_p->Head-delay_cunt;
			if(sins_cunt<0)
			{
				sins_cunt +=SINS_BUFFER_SIZE;
			}
			memcpy(&m_sins,&NAV_Data_Full_p->SINS_buffer[sins_cunt],sizeof(_SINS_BUFFER_t));
			
		}
	}
	double diff_vn[3] = { 0 };//����ʱ���ӳ��ٶȲ�
	double diff_pn[3] = { 0 };//����ʱ���ӳ�λ�ò�
	askew(m_sins.wnb, CW_);  //[wnb X]//���Գƾ�����ʽ
	Qnb2Cnb(m_sins.qnb, Cnb);//Cb2n
	
	
	matmul("NN", 3, 3, 3, 1.0, Cnb, CW_, 0.0, CW);
	matmul("NN", 3, 3, 3, 1.0, m_sins.Mpv, Cnb, 0.0, MpvCnb);
	matmul("NN", 3, 1, 3, 1.0, m_sins.Mpv, m_sins.vn, 0.0, MpvVn);
	matmul("NN", 3, 1, 3, 1.0, CW, lever, 0.0, Cwlever);  //wnb x lever
	matmul("NN", 3, 1, 3, 1.0, MpvCnb, lever, 0.0, MpvCnblever);
			matrixSum(NAV_Data_Full_p->SINS.vn, Cwlever, 3, 1, 1, vnL); //sins�ٶȱ۸˲������ֵ//����win_n
		  matrixSum(NAV_Data_Full_p->SINS.pos, MpvCnblever, 3, 1, 1, posL);//sinsλ�ñ۸˲������ֵ RG=RI+DCL,λ�ø˱�û����
	#if 0
	if (E_NAV_STATUS_IN_NAV == NAV_Data_Full_p->Nav_Status)
	{
		
		matrixSum(NAV_Data_Full_p->SINS.vn, Cwlever, 3, 1, 1, vnL); //sins�ٶȱ۸˲������ֵ//����win_n
		matrixSum(NAV_Data_Full_p->SINS.pos, MpvCnblever, 3, 1, 1, posL);//sinsλ�ñ۸˲������ֵ RG=RI+DCL,λ�ø˱�û����
		matrixSum(NAV_Data_Full_p->SINS.vn, m_sins.vn, 3, 1, -1, diff_vn);//��ǰʱ��ȥ��gps��imu�Լ��㷨����ʱ���ֵ
		matrixSum(NAV_Data_Full_p->SINS.pos, m_sins.pos, 3, 1, -1, diff_pn);//��ǰʱ��ȥ��gps��imu�Լ��㷨����ʱ���ֵ
	}
	
	else 
	{
		matrixSum(m_sins.vn, Cwlever, 3, 1, 1, vnL_); //sins�ٶȱ۸˲������ֵ//����win_n
		matrixSum(m_sins.pos, MpvCnblever, 3, 1, 1, posL_);//sinsλ�ñ۸˲������ֵ RG=RI+DCL,λ�ø˱�û����
		matrixSum(vnL_, m_sins.an, 3, 1, -timediff, vnL);   //sins�ٶ���ʱ�������ֵ
		matrixSum(posL_, MpvVn, 3, 1, -timediff, posL);        //sinsλ����ʱ�������ֵ
	}
	#endif
	timediff = 0.0;
	
#if 1
//	if (MOTION_STATUS_HEADING == g_STATIC_DETECTION.state)//��ǰû�и�ģʽ
	if(0)
	{
		for (i = 0;i < 6; i++)
		{
			if (i < 3)
			{
				Zk[i] = vnL[i] - (gpsVn[i]+ diff_vn[i]);
			}
			else
			{
				Zk[i] = posL[i - 3] - (gpsPos[i - 3] + diff_pn[i-3]);
			}
		}
		//-------����Լ���в�Zk[6]--H���󹹽�--------------------------------------------------------------
		Zk[6]=NAV_Data_Full_p->SINS.att[2]-NAV_Data_Full_p->SINS.att_pre[2];
		//-----------------------------------------------------------------------------------
	}
#endif
	else
	{
		for (i = 0; i < 6; i++)
		{
			if (i < 3)
			{
//				Zk[i] = ins->vn[i] - gpsVn[i];
				//Zk[i] = vnL[i] - (gpsVn[i] + diff_vn[i]);
				Zk[i] = vnL[i] - (gpsVn[i]) ;//*******����ʱ�䲹��****
			}
			else if(i<6)
			{
				//Zk[i] = posL[i - 3] - (gpsPos[i - 3] + diff_pn[i-3]);
				Zk[i] = posL[i - 3] - (gpsPos[i - 3]);
			}
			//else Zk[k++]=ins->att[2]-heading;
			//else Zk[k++]=0.0;
		}		

#if 0
		att_ref[0] = NAV_Data_Full_p->SINS_buffer[nGnssPosIndex].att[0];
		att_ref[1] = NAV_Data_Full_p->SINS_buffer[nGnssPosIndex].att[1];
		att_ref[2] = heading;
		for(int i=0;i<delay_cunt;i++)
		{
			att_update(NAV_Data_Full_p,NAV_Data_Full_p->im)
		}
#endif
		
		att_ref[0] = m_sins.att[0];
		att_ref[1] = m_sins.att[1];
		//att_ref[2] = heading+(timediff+4/SAMPLE_FREQ)*NAV_Data_Full_p->IMU.gyro_use[2];
		att_ref[2] = heading;
		att2qnb(att_ref,q_ref);
		qConj(NAV_Data_Full_p->SINS.qnb,q_temp);
		qnbmul(q_temp,q_ref,dq);
		qnb2att(dq,d_att);
		//Zk[6]= d_att[2];
		Zk[6]=CorrHeading(d_att[2]*RAD2DEG)*DEG2RAD;
	    //Zk[6]=NAV_Data_Full_p->SINS.att[2]-heading;
	}
	#endif
}
/******************************************************************************
*ԭ  �ͣ�void Est_Wheel_delay(_NAV_Data_Full_t *pNAV_Data_Full)//P���Q���ʼ��
*��  �ܣ��������ټ����imu��ʱ���ӳ�
*��  �룺
*��  ������
*******************************************************************************/
void Est_Wheel_delay(_NAV_Data_Full_t* pNAV_Data_Full_p)
{
	//pNAV_Data_Full_p
}
/******************************************************************************
*ԭ  �ͣ�void Init_XPQ(KF_t* kf, double x,double P, double  Q, int i)//P���Q���ʼ��
*��  �ܣ�P���Q���ʼ��
*��  �룺 
*��  ������
*******************************************************************************/
static void Init_XPQ(_KF_t* kf, double x,double P, double  Q, int i)//P���Q���ʼ��
{
	int j = 0;
	kf->Xk[i] = x;
	for (j = 0; j < NA; j++) 
	{
		kf->Pxk[i + j * NA] = kf->Pxk[j + i * NA] = i == j ? P : 0.0;
		kf->Qk[i + j * NA] = kf->Qk[j + i * NA] = i == j ? Q : 0.0;
	}
}

double M1[3*3]={0.0};
double M2[3*3]={0.0};
double M3[3*3]={0.0};

double Maa[3*3]={0.0};
double Mav[3*3]={0.0};
double Map[3*3]={0.0};

double Mva[3*3]={0.0};
double Mvv[3*3]={0.0};
double Mvp[3*3]={0.0};

double Mpv[3*3]={0.0};
double Mpp[3*3]={0.0};

double	Ft[NA*NA]={0.0};

//�߾��ȹ���phi����
void Update_Phi_HP(double* Fk,_NAV_Data_Full_t *pNAV_Data_Full,unsigned char nstep)
{
	int i		=	0;
	int j		=	0;
	double	dt 	= 	nstep*pNAV_Data_Full->SINS.ts;
		
	double sinL	=	sin(pNAV_Data_Full->SINS.pos[0]);
	double cosL	=	cos(pNAV_Data_Full->SINS.pos[0]);
	double secL	=	1.0/cos(pNAV_Data_Full->SINS.pos[0]);
	double tanL	=	tan(pNAV_Data_Full->SINS.pos[0]);
	double sin2L=	sin(2*pNAV_Data_Full->SINS.pos[0]);
	double cos2L=	cos(2*pNAV_Data_Full->SINS.pos[0]);
		
	double ve	=	pNAV_Data_Full->SINS.vn[0];
	double vn	=	pNAV_Data_Full->SINS.vn[1];
	double vu	=	pNAV_Data_Full->SINS.vn[2];

	double h	=	pNAV_Data_Full->SINS.pos[2];
	double RMh	=	pNAV_Data_Full->EARTH.RMh;
	double RNh	=	pNAV_Data_Full->EARTH.RNh;

	//��ʱ����
	double	wnin_[3]={0.0};
	double	vnx[3*3]={0.0};
	double	wnienx[3*3]={0.0};
	double	Mvv_[3*3]={0.0};
	double	Mvp_[3*3]={0.0};


	//�ο�����ĵ�10.2.5�� �����ߵ�ԭ��4.2.5
/**********************************************************/	
	//�������M1
	memset(M1,0,sizeof(M1));
	M1[1]	=	-WIE*sinL;
	M1[2]	=	WIE*cosL;
//	M1[3]	=	-WIE*sinL;
//	M1[6]	=	WIE*cosL;
	
	//�������M2
	memset(M2,0,sizeof(M2));
	M2[2]	=	ve*secL*secL/RNh;
	M2[6]	=	vn/(RMh*RMh);
	M2[7]	=	-ve/(RNh*RNh);
	M2[8]	=	-ve*tanL/(RNh*RNh);

	//�������M3
	memset(M3,0,sizeof(M3));

//	M3[1]	=	-2*Grs80_Beta3*h*cos2L;
//	M3[2]	=	-G0*(Grs80_Beta0-4*Grs80_Beta1*cos2L)*sin2L;//ע���ǳ��������ٶ�
//	M3[7]	=	-Grs80_Beta3*sin2L;
//	M3[8]	=	Grs80_Beta2;


/**********************************************************/
	//�������Maa
	memset(Maa,0,sizeof(Maa));
	for (i = 0; i < 3; i++)
	{
		wnin_[i] = (-pNAV_Data_Full->EARTH.wnin[i]);
	}
	askew(wnin_, Maa);

	//����Mav
	memset(Mav,0,sizeof(Mav));
	Mav[1]	=	1.0/RNh;
	Mav[2]	=	tanL/RNh;
	Mav[3]	=	-1.0/RMh;

	//����Map
	memset(Map,0,sizeof(Map));
	matrixSum(M1, M2, 3, 3, 1, Map);

/**********************************************************/
	//����Mva
	memset(Mva,0,sizeof(Mva));
	askew(pNAV_Data_Full->SINS.fn, Mva);

	//����Mvv
	memset(Mvv,0,sizeof(Mvv));
	askew(pNAV_Data_Full->SINS.vn, vnx);
	matmul("NN", 3, 3, 3, 1.0, vnx, Mav, 0.0, Mvv_);
	askew(pNAV_Data_Full->EARTH.wnien, wnienx);
	matrixSum(Mvv_, wnienx, 3, 3, -1, Mvv);

	//����Mvp
	memset(Mvp,0,sizeof(Mvp));
	matrixSum(M2, M1, 3, 3, 2, Mvp_);
	matmul("NN", 3, 3, 3, 1.0, vnx, Mvp_, 0.0, Mvp);
	//matrixSum(Mvp, M3, 3, 3, 1, Mvp);//******
	
/**********************************************************/	
	//����Mpv
	memset(Mpv,0,sizeof(Mpv));
	Mpv[1]	=	secL/RNh;
	Mpv[3]	=	1.0/RMh;
	Mpv[8]	=	1.0;

	//����Mpp
	memset(Mpp,0,sizeof(Mpp));
	Mpp[1]	=	ve*secL*tanL/RNh;
	Mpp[6]	=	-vn/(RMh*RMh);	
	Mpp[7]	=	-ve*secL/(RNh*RNh);
/***************************����f����**************************************/
	memset(Ft,0,sizeof(Ft));
	//i������ѭ��,j������ѭ��
	for (i = 0; i < NA; i++)
		{
			for (j = 0; j < NA; j++)
			{
				if (i < 3 && j < 3) 
				{
					Ft[i + j * NA] = Maa[i + j * 3];//Ft[i + j * NA] = Maa[i + j * 3];
				}
				else if (i >= 3 && i < 6 && j < 3) 
				{
					Ft[i + j * NA] = Mva[i - 3 + j * 3];				
				}
				else if (i < 3 && j >= 3 && j < 6) 
				{
					Ft[i + j * NA] = Mav[i + (j - 3) * 3]; //Ft[i + j * NA] = Mav[i + (j - 3) * 3]; 
				}
				else if (i >= 3 && i < 6 && j >= 3 && j < 6) 
				{
					Ft[i + j * NA] = Mvv[(i - 3) + (j - 3) * 3];	//Ft[i + j * NA] = Mvv[(i - 3) + (j - 3) * 3];	
				}
				else if (i >= 6 && i < 9 && j >= 3 && j < 6) 
				{
					Ft[i + j * NA] = Mpv[(i - 3 * 2) + (j - 3) * 3]; 
				}
				else if (i < 3 && j >= 6 && j < 9) 
				{
					Ft[i + j * NA] = Map[i + (j - 3 * 2) * 3];//Ft[i + j * NA] = Map[i + (j - 3 * 2) * 3];
				}
				else if (i >= 3 && i < 6 && j >= 6 && j < 9) {
					Ft[i + j * NA] = Mvp[(i - 3) + (j - 3 * 2) * 3];//Ft[i + j * NA] = Mvp[(i - 3) + (j - 3 * 2) * 3];
				}
				else if (i >= 6 && i < 9 && j >= 6 && j < 9) {
					Ft[i + j * NA] = Mpp[(i - 3* 2) + (j - 3 * 2) * 3];//Ft[i + j * NA] = Mpp[(i - 3* 2) + (j - 3 * 2) * 3];
				}
				else if (i < 3 && j >= 9 && j < 12) 
				{
					Ft[i + j * NA] = -pNAV_Data_Full->SINS.Cb2n[i + (j - 3 * 3) * 3];
				}
				else if (i >= 3 && i < 6 && j >= 12 && j < 15) 
				{
					Ft[i + j * NA] = pNAV_Data_Full->SINS.Cb2n[(i - 3) + (j - 3 * 4) * 3];
				}
				else if (i==j && i>=9 && i<=11)
				{
					Ft[i + j * NA] =0.0;// -1 / TGB;
				}
				else if (i == j && i >= 12 && i <= 14)
				{
					Ft[i + j * NA] =0.0;// -1 / TAB;
				}
				//����z�����ݱ������ӹ���-wang
				else if (NA>15 && i == 8 && j == 15)
				{
					Ft[i + j * NA] = -pNAV_Data_Full->SINS.wnb[2]; //
				}
			}
		}

/***************************����phi����**************************************/
	EyeMatrix(Fk, NA, 1.0);
	//Fk=I+Ft*dt
	matrixSum(Fk, Ft, NA, NA, dt, Fk);
}



#if 0
unsigned char Update_Phi_2(double* Fk,_NAV_Data_Full_t *pNAV_Data_Full,unsigned char nstep)//KF����Phi����,����һ��״̬���¾���
{
	int i,j;
	int NN=3;
	//�����������ʱ��
	float dt = nstep*pNAV_Data_Full->SINS.ts;
	//Phi=I+F*dt
	double* Ft = zeros(NA, NA);
	//��ز���
	double tl = pNAV_Data_Full->EARTH.tL;
	double secl = 1.0/pNAV_Data_Full->EARTH.cL;
	double f_RMh = 1.0/pNAV_Data_Full->EARTH.RMh;
	double f_RNh = 1.0/pNAV_Data_Full->EARTH.RNh;
	double f_clRNh = 1.0/pNAV_Data_Full->EARTH.clRNh;
	double f_RMh2 = f_RMh * f_RMh;
	double f_RNh2 = f_RNh * f_RNh;
	//����ϵ�ٶ�
	double vn[3]={0};
	double vE_clRNh=0.0;
	double vE_RNh2=0.0;
	double vN_RMh2=0.0;
    double scl=0.0;

	double* wnin_ = zeros(3, 1); 
	double* Maa = zeros(3, 3), * Mav = zeros(3, 3), * Map = zeros(3, 3);
	double* Mva = zeros(3, 3), * Mvv = zeros(3, 3), * Mvp = zeros(3, 3);
	double* Mpv = zeros(3, 3), * Mpp = zeros(3, 3);
	double* Mvv_ = zeros(3, 3), * Mvp_ = zeros(3, 3);
	double* Mp1 = zeros(3, 3), * Mp2 = zeros(3, 3);
	double* Avn = zeros(3, 3), * Awn = zeros(3, 3);
	double* Cnb = zeros(3, 3);
	double* I = eyes(NA);
	
	if(		NULL == Ft || NULL == wnin_
		||	NULL == Maa || NULL == Mav || NULL == Map || NULL == Mva || NULL == Mvv || NULL == Mvp || NULL == Mpv || NULL == Mpp || NULL == Mvv_ || NULL == Mvp_
		||	NULL == Mp1 || NULL == Mp2 || NULL == Avn || NULL == Awn || NULL == Cnb || NULL == I	
	 )
	{
		inav_log(INAVMD(LOG_ERR),"P is NULL");
		free(Ft); free(wnin_); free(Maa); free(Mav); free(Map); free(Mva); free(Mvv); free(Mvp);
		free(Mpv); free(Mpp); free(Mvv_); free(Mvp_); free(Mp1); free(Mp2); free(Avn); free(Awn);
		free(Cnb); free(I);
		return RETURN_FAIL;	
	}
	
	for (i = 0; i < 3; i++)
	{
		wnin_[i] = (-pNAV_Data_Full->EARTH.wnin[i]);
	}
	
	//��ȡ����ϵ�ٶ�
	for (i = 0; i < 3; i++)
	{
		vn[i] = pNAV_Data_Full->SINS.vn[i];
	}
	vE_clRNh= vn[0] * f_clRNh;
	vE_RNh2 = vn[0] * f_RNh2;
	vN_RMh2 = vn[1] * f_RMh2;

	Mp1[1] = (-pNAV_Data_Full->EARTH.wnie[2]);
	Mp1[2] = pNAV_Data_Full->EARTH.wnie[1];
	Mp2[2] = vE_clRNh * secl; 
	Mp2[6] = vN_RMh2; 
	Mp2[7] = (-vE_RNh2); 
	Mp2[8] = (-vE_RNh2 * tl);
	askew(vn, Avn);
	askew(pNAV_Data_Full->EARTH.wnien, Awn);
	
	askew(wnin_, Maa);
	Mav[1] = f_RNh; 
	Mav[2] = f_RNh * tl; 
	Mav[3] = (-f_RMh);
	matrixSum(Mp1, Mp2, 3, 3, 1, Map);

	askew(pNAV_Data_Full->SINS.fn, Mva);

	matmul("NN", 3, 3, 3, 1.0, Avn, Mav, 0.0, Mvv_);
	matrixSum(Mvv_, Awn, 3, 3, -1, Mvv);

	matrixSum(Mp1, Map, 3, 3, 1, Mvp_);
	matmul("NN", 3, 3, 3, 1.0, Avn, Mvp_, 0.0, Mvp);

	scl = pNAV_Data_Full->EARTH.sL *pNAV_Data_Full->EARTH.cL;
	Mvp[2] = Mvp[2] - G0 * (5.27094e-3 * 2 + 2.32718e-5 * 4 * pNAV_Data_Full->EARTH.sL * pNAV_Data_Full->EARTH.sL) * scl;
	Mvp[8] = Mvp[8] + 3.086e-6;

	Mpp[1] = vE_clRNh * tl; 
	Mpp[6] = (-vN_RMh2); 
	Mpp[7] = (-vE_RNh2) * secl;

	//��������任����
	Qnb2Cnb(pNAV_Data_Full->SINS.qnb, Cnb);

	for (i = 0; i < NA; i++)
	{
		for (j = 0; j < NA; j++)
		{
			if (i < 3 && j < 3) {
				switch (i)
				{
				case 0:Ft[i + j * NA] = Maa[i + j * NN]; break;
				case 1:Ft[i + j * NA] = Maa[i + j * NN]; break;
				case 2:Ft[i + j * NA] = Maa[i + j * NN]; break;
				}
			}
			else if (i >= 3 && i < 6 && j < 3) {
				switch (i)
				{
				case 3:Ft[i + j * NA] = Mva[i - NN + j * NN]; break;
				case 4:Ft[i + j * NA] = Mva[i - NN + j * NN]; break;
				case 5:Ft[i + j * NA] = Mva[i - NN + j * NN]; break;
				}
			}
			else if (i < 3 && j >= 3 && j < 6) {
				switch (i)
				{
				case 0:Ft[i + j * NA] = Mav[i + (j - NN) * NN]; break;
				case 1:Ft[i + j * NA] = Mav[i + (j - NN) * NN]; break;
				case 2:Ft[i + j * NA] = Mav[i + (j - NN) * NN]; break;
				}
			}
			else if (i >= 3 && i < 6 && j >= 3 && j < 6) {
				switch (i)
				{
				case 3:Ft[i + j * NA] = Mvv[(i - NN) + (j - NN) * NN]; break;
				case 4:Ft[i + j * NA] = Mvv[(i - NN) + (j - NN) * NN]; break;
				case 5:Ft[i + j * NA] = Mvv[(i - NN) + (j - NN) * NN]; break;
				}
			}
			else if (i >= 6 && i < 9 && j >= 3 && j < 6) {
				switch (i)
				{
				case 6:Ft[i + j * NA] = pNAV_Data_Full->SINS.Mpv[(i - NN * 2) + (j - NN) * NN]; break;
				case 7:Ft[i + j * NA] = pNAV_Data_Full->SINS.Mpv[(i - NN * 2) + (j - NN) * NN]; break;
				case 8:Ft[i + j * NA] = pNAV_Data_Full->SINS.Mpv[(i - NN * 2) + (j - NN) * NN]; break;
				}
			}
			else if (i < 3 && j >= 6 && j < 9) {
				Ft[i + j * NA] = Map[i + (j - NN * 2) * NN];
			}
			else if (i >= 3 && i < 6 && j >= 6 && j < 9) {
				Ft[i + j * NA] = Mvp[(i - NN) + (j - NN * 2) * NN];
			}
			else if (i >= 6 && i < 9 && j >= 6 && j < 9) {
				Ft[i + j * NA] = Mpp[(i - NN * 2) + (j - NN * 2) * NN];
			}
			else if (i < 3 && j >= 9 && j < 12) {
				Ft[i + j * NA] = -Cnb[i + (j - NN * 3) * NN];
			}
			else if (i >= 3 && i < 6 && j >= 12 && j < 15) {
				Ft[i + j * NA] = Cnb[(i - NN) + (j - NN * 4) * NN];
			}
		}
	}

	EyeMatrix(Fk, NA, 1.0);
	//Fk=I+Ft*dt
	matrixSum(Fk, Ft, NA, NA, dt, Fk);
}
#endif
/******************************************************************************
*ԭ  �ͣ�void Update_Phi(INSRESULT * ins, double* Fk,I_NAV_INS * navins,uint8_t nstep)
*��  �ܣ�����phi����
*��  �룺 
*��  ������
*******************************************************************************/
static void Update_Phi(double* Fk,_NAV_Data_Full_t *NAV_Data_Full_temp,unsigned char nstep)//KF����Phi����,����һ��״̬���¾���
{
	int i,j;
	double M_demp[9]  = {0,0,0,0,0,0,0,0,0};
//	double M_demp1[9] = {0,0,0,0,0,0,0,0,0};
//	double M_demp2[9] = {0,0,0,0,0,0,0,0,0};
//	double V_demp[3]  = {0,0,0};
	//double V_demp1[3] = {0,0,0};
	//double V_demp2[3] = {0,0,0};

	//float dt = nstep*NAV_Data_Full_temp.SINS.ts;
	//double f_RMh,f_RNh,f_RNhcL;
	//f_RNh = 1/NAV_Data_Full_temp.EARTH.RNh;
	//f_RMh = 1/NAV_Data_Full_temp.EARTH.RMh;
	//f_RNhcL = 1/NAV_Data_Full_temp.EARTH.clRNh;

	double dt = nstep*NAV_Data_Full_temp->SINS.ts;
	double f_RMh,f_RNh,f_RNhcL;
	f_RNh = 1/NAV_Data_Full_temp->EARTH.RNh;
	f_RMh = 1/NAV_Data_Full_temp->EARTH.RMh;
	f_RNhcL = 1/NAV_Data_Full_temp->EARTH.clRNh;

#if 0	
	for(i = 0;i<NA;i++)
	{
		for(j = 0;j<NA;j++)
		{			
			if(i == j)
			{
				Fk[i+i*NA] = 1;
			}
			else
			{
				Fk[i+j*NA] = 0;
			}
		}
	}
#endif	
	EyeMatrix(Fk, NA, 1.0);
	#if 0
	for (i = 9;i<NA-3;i++)
	{
		Fk[i + i * NA] = 1 - 1.0/1000 * 0.005;
	}
	for (i = 12; i < NA; i++)
	{
		Fk[i + i * NA] = 1 - 1.0 / 1000 * 0.005;
	}
	#endif
//////////////////////��ʼ��ֵ///////////////////////////////
	//att	
	{	
		//Maa
	//     Maa = [ 0,               ins.eth.wnin(3),-ins.eth.wnin(2);  % Maa = -askew(ins.eth.wnin);
	//            -ins.eth.wnin(3), 0,               ins.eth.wnin(1); 
	//             ins.eth.wnin(2),-ins.eth.wnin(1), 0 ];	
		
		
	//	askew(navins->earth.wnin, M_demp);
	//    for(i = 0;i<3;i++)
	//	{
	//		for(j = 0;j<3;j++)
	//		{
	//			Fk[i+j*NA] += (-M_demp[i+j*3]*dt);
	//		}
	//	}
		//Mav
	//	M_demp[0] = 0;                                  M_demp[3] = 1/navins->earth.RMh; M_demp[6] = 0;
	//	M_demp[1] = 1/navins->earth.RNh;                M_demp[4] = 0;                   M_demp[7] = 0;
	//	M_demp[2] = navins->earth.RNh*navins->earth.tl; M_demp[5] = 0;                   M_demp[8] = 0;
	//	Fk[0+4*NA] += f_RMh*dt;
	//	Fk[1+3*NA] += f_RNh*dt;
	//	Fk[2+3*NA] += f_RNh*navins->earth.tl*dt;
		
		//Map
	//    M_demp[0] = 0;                                     M_demp[3] = 0;                   M_demp[6] = navins->ins.vn[1]*f_RMh*f_RMh;
	//	M_demp[1] = -navins->earth.wnie[1];                M_demp[4] = 0;                   M_demp[7] = navins->ins.vn[0]*f_RNh*f_RNh;
	//	M_demp[2] = navins->earth.wnie[2]+navins->ins.vn[0]*navins->earth.cl*navins->earth.cl*f_RNh;             M_demp[5] = 0;                   M_demp[8] = -navins->ins.vn[0]*f_RNh*f_RNh*navins->earth.tl;
	//    for(i = 0;i<3;i++)
	//	{
	//		for(j = 0;j<3;j++)
	//		{
	//			Fk[i+(j+6)*NA] += M_demp[i+j*3]*dt;
	//		}
	//	}

		//Mag   -Cnb
		for(i = 0;i<3;i++)
		{
			for(j = 0;j<3;j++)
			{
				//Fk[i+(j+9)*NA] += (-NAV_Data_Full_temp.SINS.Cb2n[i+j*3]*dt);
				Fk[i+(j+9)*NA] += (-NAV_Data_Full_temp->SINS.Cb2n[i+j*3]*dt);
			}
		}

	}
	//vn	
	{
		//Mva	
		//askew(NAV_Data_Full_temp.SINS.fn, M_demp);
		askew(NAV_Data_Full_temp->SINS.fn, M_demp);
		for(i = 0;i<3;i++)
		{
			for(j = 0;j<3;j++)
			{
				Fk[i+3+j*NA] += M_demp[i+j*3]*dt;
			}
		}
	 
		//Mvv
	//  askew(navins->earth.wnien, M_demp1);
	//  askew(ins->vn, M_demp2);
	//  matrixSum(M_demp1, M_demp2, 3, 3, 1, M_demp);
	//	for(i = 0;i<3;i++)
	//	{
	//		for(j = 0;j<3;j++)
	//		{
	//			Fk[i+3+(j+3)*NA] += M_demp[i+j*3]*dt;
	//		}
	//	}
		//Mvp
	//  double g0 = 9.7803267714;  
	//	double scl = navins->earth.sl*navins->earth.cl;
	//	double sl2 = navins->earth.sl*navins->earth.sl;
	//	
	//  M_demp1[2] = -g0*(5.27094e-3*2+2.32718e-5*4*sl2)*scl; 
	//	M_demp1[8] =  3.086e-6;  
	//	
	//	Fk[5+(6)*NA] += M_demp1[2]*dt;
	//	Fk[5+(8)*NA] += M_demp1[8]*dt;
		
	//	for(i = 0;i<3;i++)
	//	{
	//		for(j = 0;j<3;j++)
	//		{
	//			Fk[i+3+(j+6)*NA] += M_demp[i+j*3]*dt;
	//		}
	//	}
		
		
		//Mvf
		for(i = 0;i<3;i++)
		{
			for(j = 0;j<3;j++)
			{
				//Fk[i+3+(j+12)*NA] += NAV_Data_Full_temp.SINS.Cb2n[i+j*3]*dt;
				Fk[i+3+(j+12)*NA] += NAV_Data_Full_temp->SINS.Cb2n[i+j*3]*dt;
			}
		}	 
	}
	//pos
	{	
		//Mpa
		//O
		
		
		//Mpv
	//	M_demp[3] = 1/navins->earth.RMh;
	//	M_demp[1] = 1/(navins->earth.RNh*navins->earth.cl);
	//	M_demp[8] = 1;

		Fk[6+4*NA] += f_RMh*dt;
		Fk[7+3*NA] += f_RNhcL*dt;
		Fk[8+5*NA] += 1*dt;
		
//		Fk[6+4*NA] += dt;
//		Fk[7+3*NA] += dt;
//		Fk[8+5*NA] += dt;

		//Mpp
	//	M_demp[0] = 0;                                  M_demp[3] = 0;                   M_demp[6] = -navins->ins.vn[1]/(navins->earth.RMh)/(navins->earth.RMh);
	//	M_demp[1] = -navins->earth.sl*1;                M_demp[4] = 0;                   M_demp[7] = -navins->ins.vn[0]/(navins->earth.RNh)/(navins->earth.RNh)/navins->earth.cl;
	//	M_demp[2] = 0;                                  M_demp[5] = 0;                   M_demp[8] = 0;
		
	//	Fk[6+8*NA] = -navins->ins.vn[1]*f_RMh*f_RMh*dt;
	//	Fk[7+6*NA] = -navins->earth.sl*1*dt;
	//	Fk[7+8*NA] = -navins->ins.vn[0]*f_RNh*f_RNh*navins->earth.cl*dt;
	}
	
	//eb db
	{
		//
	//	for(i = 9;i<12;i++)
	//	{
	//		Fk[i+i*NA] += (-dt/1000);

	//	}
	//	for(i = 12;i<NA;i++)
	//	{
	//		Fk[i+i*NA] += (-dt/1000);
	//	}
	}
}


 
/******************************************************************************
*ԭ  �ͣ�void KfFeedback(KF_t* kf, INSRESULT* ins,int nstep)  //Kf״̬����
*��  �ܣ�״̬����
*��  �룺 
*��  ������
*******************************************************************************/

static void KfFeedback(_NAV_Data_Full_t* NAV_Data_Full_p,unsigned char nstep) //Kf״̬����
{
	double phi[3] = {0};
	double dt = (double)nstep*NAV_Data_Full_p->SINS.nts;
	int i;
	//********dv,dpos*******
	for (i = 0; i < 3; i++)
	{
		phi[i] = NAV_Data_Full_p->KF.Xk[i];
		NAV_Data_Full_p->SINS.vn[i]  -= NAV_Data_Full_p->KF.Xk[i + 3];
		NAV_Data_Full_p->SINS.pos[i] -= NAV_Data_Full_p->KF.Xk[i + 3 * 2];
		NAV_Data_Full_p->SINS.eb[i] += NAV_Data_Full_p->KF.Xk[i + 3 * 3];
		NAV_Data_Full_p->SINS.db[i] += NAV_Data_Full_p->KF.Xk[i + 3 * 4];
	}

	//****datt*****
	qdelphi(NAV_Data_Full_p->SINS.qnb, phi, NAV_Data_Full_p->SINS.qnb);
	qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
	Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
	Mat_Tr(3, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.Cn2b);
	//if (NAV_Data_Full_p->ZUPT_flag)//��ֹ******�����Ƚ��ã�*********
	//{
	//	NAV_Data_Full_p->SINS.eb[0] = 0.9999 * NAV_Data_Full_p->SINS.eb[0] + 0.0001 * NAV_Data_Full_p->Mgyr[0];
	//	NAV_Data_Full_p->SINS.eb[1] = 0.9999 * NAV_Data_Full_p->SINS.eb[1] + 0.0001 * NAV_Data_Full_p->Mgyr[1];
	//	NAV_Data_Full_p->SINS.eb[2] = 0.9999 * NAV_Data_Full_p->SINS.eb[2] + 0.0001 * NAV_Data_Full_p->Mgyr[2];
	//}
	//
	for (i = 0; i < NA; i++)
	{
		NAV_Data_Full_p->KF.Xk[i] = 0.0;
	}
	NAV_Data_Out.lat_std=NAV_Data_Full_p->KF.Pxk[6+i*NA];
	NAV_Data_Out.lon_std=NAV_Data_Full_p->KF.Pxk[7+i*NA];
	NAV_Data_Out.alt_std=NAV_Data_Full_p->KF.Pxk[8+i*NA];
	NAV_Data_Out.pitch_std=NAV_Data_Full_p->KF.Pxk[0+0*NA];
	NAV_Data_Out.roll_std=NAV_Data_Full_p->KF.Pxk[1+1*NA];
	NAV_Data_Out.heading_std=NAV_Data_Full_p->KF.Pxk[2+2*NA];

#if 0  //*****ע�������Ĵ�������****



//	double tmpatt2=0.0;
//	double KF_nts;
//	KF_nts = ((double)NAV_Data_Full_p->SINS.nts*KLMAN_FILTER_SETP_SUM);

#if 1
//	for (i = 0; i < 3; i++)
//	{
//		phi[i] = NAV_Data_Full_p->KF.Xk[i];						                NAV_Data_Full_p->KF.Xk[i] = 0.0;
//		NAV_Data_Full_p->SINS.vn[i]  -= NAV_Data_Full_p->KF.Xk[i + 3];			NAV_Data_Full_p->KF.Xk[i + 3] = 0.0;
//		NAV_Data_Full_p->SINS.pos[i] -= NAV_Data_Full_p->KF.Xk[i + 3 * 2];		NAV_Data_Full_p->KF.Xk[i + 3 * 2] = 0.0;
//		NAV_Data_Full_p->SINS.eb[i] +=(NAV_Data_Full_p->KF.Xk[i + 3 * 3]);		NAV_Data_Full_p->KF.Xk[i + 3 * 3] = 0.0;
//		NAV_Data_Full_p->SINS.db[i] +=(NAV_Data_Full_p->KF.Xk[i + 3 * 4]);		NAV_Data_Full_p->KF.Xk[i + 3 * 4] =  0.0;
//	}
    //datt
	//if (E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source)
	//if(norm(NAV_Data_Full_p->SINS.vn,3)>0.5)
	//��̬�ɹ۲��ԽϲӦ��ʹ�ò��ַ���
	
//	for (i = 0; i < 3; i++)
//	{
//		phi[i] = NAV_Data_Full_p->KF.Xk[i];
//#if 0
//		if (E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source
//			|| E_FUNSION_MOTION == NAV_Data_Full_p->KF.fusion_source
//		
//			//&& fabs(NAV_Data_Full_p->SINS.wnb_pre[2] * RAD2DEG) > 1
//			)
//		{
//			phi[i] = phi[i] / (1+fabs(NAV_Data_Full_p->SINS.wnb_pre[2] * RAD2DEG))/ (1 + fabs(NAV_Data_Full_p->SINS.wnb_pre[2] * RAD2DEG));
//			if (fabs(NAV_Data_Full_p->SINS.wnb_pre[2] * RAD2DEG) > 3
//				)
//				phi[i] = 0;
//		}
//		/*if (NAV_Data_Full_p->ODS.WheelSpeed_ave == 0
//			&& NAV_Data_Full_p->Nav_Status < 4
//			&& combineData.canInfo.flag == 1)
//		{
//			phi[i] = 0.1* phi[i];
//		}*/
//		else if(sqrt(NAV_Data_Full_p->GPS.vn * NAV_Data_Full_p->GPS.vn + NAV_Data_Full_p->GPS.ve * NAV_Data_Full_p->GPS.ve)<0.05 
//			&& NAV_Data_Full_p->KF.fusion_source == E_FUNSION_GPS)
//			phi[i] = 0.1 * phi[i];
//		#endif
//	}
    
	
//	//dv
//	for (i = 0; i < 3; i++)
//	{
//		NAV_Data_Full_p->SINS.vn[i]  -= NAV_Data_Full_p->KF.Xk[i + 3];			
//		//NAV_Data_Full_p->KF.Xk[i + 3] = 0.0;
//	}
//	//dpos
//	if (E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source)
//	{
//		//if(E_NAV_STATUS_IN_NAV== NAV_Data_Full_p->Nav_Status)
//		//if(E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source )
//		{
//			NAV_Data_Full_p->SINS.pos[0] -= NAV_Data_Full_p->KF.Xk[0 + 3 * 2];	
//			NAV_Data_Full_p->SINS.pos[1] -= NAV_Data_Full_p->KF.Xk[1 + 3 * 2];
//			NAV_Data_Full_p->SINS.pos[2] -= NAV_Data_Full_p->KF.Xk[2 + 3 * 2];	
//		}
//		//NAV_Data_Full_p->KF.Xk[0 + 3 * 2] = 0.0;
//		//NAV_Data_Full_p->KF.Xk[1 + 3 * 2] = 0.0;
//		//NAV_Data_Full_p->KF.Xk[2 + 3 * 2] = 0.0;
////		NAV_Data_Full_p->SINS.pos[0] -= NAV_Data_Full_p->KF.Xk[0 + 3 * 2]/NAV_Data_Full_p->EARTH.RMh;		
////		NAV_Data_Full_p->KF.Xk[0 + 3 * 2] = 0.0;
////		NAV_Data_Full_p->SINS.pos[1] -= NAV_Data_Full_p->KF.Xk[1 + 3 * 2]/NAV_Data_Full_p->EARTH.clRNh;		
////		NAV_Data_Full_p->KF.Xk[1 + 3 * 2] = 0.0;
////		NAV_Data_Full_p->SINS.pos[2] -= NAV_Data_Full_p->KF.Xk[2 + 3 * 2];		
////		NAV_Data_Full_p->KF.Xk[2 + 3 * 2] = 0.0;
//	}
//	//if (E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source
//	//	&& fabs(NAV_Data_Full_p->SINS.wnb_pre[2] * RAD2DEG) < 10)
//	//{
//	//	NAV_Data_Full_p->SINS.pos[0] -= measure_count * 0.005 * NAV_Data_Full_p->KF.Xk[0 + 3]/2/ NAV_Data_Full_p->EARTH.RMh;
//	//	NAV_Data_Full_p->SINS.pos[1] -= measure_count * 0.005 * NAV_Data_Full_p->KF.Xk[1 + 3]/2/ NAV_Data_Full_p->EARTH.clRNh;
//	//	NAV_Data_Full_p->SINS.pos[2] -= measure_count * 0.005 * NAV_Data_Full_p->KF.Xk[2 + 3]/2;
//	//	measure_count = 0;
//	//}
	
	//*******************
	//datt
	for (i = 0; i < 3; i++)
	{
		phi[i] = NAV_Data_Full_p->KF.Xk[i];
	}
		//dv
	for (i = 0; i < 3; i++)
	{
		NAV_Data_Full_p->SINS.vn[i]  -= NAV_Data_Full_p->KF.Xk[i + 3];			
	}
		//dpos
	{
		NAV_Data_Full_p->SINS.pos[0] -= NAV_Data_Full_p->KF.Xk[0 + 3 * 2];	
		NAV_Data_Full_p->SINS.pos[1] -= NAV_Data_Full_p->KF.Xk[1 + 3 * 2];
		NAV_Data_Full_p->SINS.pos[2] -= NAV_Data_Full_p->KF.Xk[2 + 3 * 2];	
	}
	for (i = 0;i<9;i++)
	{
		NAV_Data_Full_p->KF.Xk[i] = 0.0;
	}
	//wb db
	for (i = 0; i < 3; i++)
	{
		//�������ǰ�ڣ���������ƫ����
		//if(E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source )
		//if(E_NAV_STATUS_SYSTEM_STANDARD == NAV_Data_Full_p->Nav_Status
			//)
		//if (E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source 
		//	||(NAV_Data_Full_p->ODS.WheelSpeed_Back_Right == 0
		//		&& NAV_Data_Full_p->Nav_Status <4) )
		//{		
		//	NAV_Data_Full_p->KF.Xk[i + 3 * 3] = 0.1 * NAV_Data_Full_p->KF.Xk[i + 3 * 3];
		//	//NAV_Data_Full_p->KF.Xk[i + 3 * 4] = 0.0;
		//}
		
//		if (E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source
//			//&&fabs(NAV_Data_Full_p->SINS.wnb_pre[2] * RAD2DEG) > 10
//			)
//		{
//			NAV_Data_Full_p->KF.Xk[i + 3 * 3] = 0;
//			NAV_Data_Full_p->KF.Xk[i + 3 * 4] = 0;
//		}
		
			NAV_Data_Full_p->SINS.eb[i] +=(NAV_Data_Full_p->KF.Xk[i + 3 * 3]);	
			NAV_Data_Full_p->SINS.db[i] +=(NAV_Data_Full_p->KF.Xk[i + 3 * 4]);
		
//		if (NAV_Data_Full_p->SINS.db[i] < -0.3)
//		{
//			NAV_Data_Full_p->SINS.db[i] = -0.3;
//		}
//		if (NAV_Data_Full_p->SINS.db[i] > 0.3)
//		{
//			NAV_Data_Full_p->SINS.db[i] = 0.3;
//		}
		NAV_Data_Full_p->KF.Xk[i + 3 * 3] = 0.0;
		NAV_Data_Full_p->KF.Xk[i + 3 * 4] =  0.0;
	}
//	if(NA>15)
//	{	if (E_FUNSION_WHEEL == NAV_Data_Full_p->KF.fusion_source
//		//&&fabs(NAV_Data_Full_p->SINS.wnb_pre[2] * RAD2DEG) > 10
//		)
//		{
//			NAV_Data_Full_p->KF.Xk[15] = 0.0;
//		}

//		NAV_Data_Full_p->SINS.es += NAV_Data_Full_p->KF.Xk[15];
//		NAV_Data_Full_p->KF.Xk[15] = 0.0;
//	}
	
	
#endif
	//if(E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source )
	{
		qdelphi(NAV_Data_Full_p->SINS.qnb, phi, NAV_Data_Full_p->SINS.qnb);
	}

	//������̬����Ԫ��ת��̬
	//if(E_FUNSION_GPS == NAV_Data_Full_p->KF.fusion_source)
	{
		qnb2att( NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att); 
		Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
		Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.Cn2b);
#if 0
		for (i = 0; i < 3; i++)
		{
			NAV_Data_Full_p->SINS.att_pre[i] = NAV_Data_Full_p->SINS.att[i];
		}
		if(E_NAV_STATUS_SYSTEM_STANDARD ==  NAV_Data_Full_p->Nav_Status)
		{
			if(fabs(NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG)>2.0f)
			{
			NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2]=NAV_Data_Full_p->SINS.att[2]*RAD2DEG
				+NAV_Data_Full_p->GPS.Heading
				-NAV_Data_Full_p->Param.gnssAtt_from_vehicle[2];
			}
		}
#endif		
	}

	

#if 0	
	else//����̼Ƶ�Լ��ʱ�򣬺���������ƫ������
	{
		tmpatt2 = NAV_Data_Full_p->SINS.att[2];
		qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.att);
		NAV_Data_Full_p->SINS.att[2]=tmpatt2;
		att2qnb(NAV_Data_Full_p->SINS.att,NAV_Data_Full_p->SINS.qnb);
		Qnb2Cnb(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->SINS.Cb2n);
		Mat_Tr(3,NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->SINS.Cn2b);
		for (i = 0; i < 3; i++)
		{
			NAV_Data_Full_p->SINS.att_pre[i] = NAV_Data_Full_p->SINS.att[i];
		}
		
	}
#endif	
	//��ƫ�޷�***********
	 /*
	//��ƫ�޷����¾�̬ʱ������쳣����
	if(0)
	{
		for (i = 0; i < 3; i++)
		{	
			if(NAV_Data_Full_p->SINS.eb[i]> 0.5*DEG2RAD)
			{
#ifdef linux
			   //inav_log(INAVMD(LOG_ERR),"eb over max! eb[%d]=%f",i,NAV_Data_Full_p->SINS.eb[i]);
#endif
				NAV_Data_Full_p->SINS.eb[i] = 0.5*DEG2RAD;
			}
			else if(NAV_Data_Full_p->SINS.eb[i]< (-0.5*DEG2RAD))
			{
#ifdef linux
			   //inav_log(INAVMD(LOG_ERR),"eb over min! eb[%d]=%f",i,NAV_Data_Full_p->SINS.eb[i]);
#endif			
				NAV_Data_Full_p->SINS.eb[i] = -0.5*DEG2RAD;
			}
		}
	}
	
	if(0)
	{	
		
		for (i = 0; i < 3; i++)
		{
			if(NAV_Data_Full_p->SINS.db[i]> (10*MG))
			{
#ifdef linux
			   //inav_log(INAVMD(LOG_ERR),"db over max! db[%d]=%f",i,NAV_Data_Full_p->SINS.db[i]);
#endif			
				NAV_Data_Full_p->SINS.db[i] = (10*MG);
			}
			else if(NAV_Data_Full_p->SINS.db[i]<(-10*MG))
			{
#ifdef linux
			   //inav_log(INAVMD(LOG_ERR),"db over min! db[%d]=%f",i,NAV_Data_Full_p->SINS.db[i]);
#endif				
				NAV_Data_Full_p->SINS.db[i] = (-10*MG);
			}	
			
		}
	}	//��ƫ�޷�
	*/


#endif

}

//////////////////////////////////////////end/////////////////////////////////////////////////////
