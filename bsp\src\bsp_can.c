#include "bsp_can.h"
#include "string.h"
#include "INS_Data.h"
#include "bsp_gpio.h"
#include "bsp_tim.h"
#include "math.h"
#include "Time_Unify.h"
#include "fpgad.h"

uint8_t g_gear_detected = 0;
uint8_t g_front_detected = 0;
uint8_t g_back_detected = 0;

uint8_t g_std_type_angle = 0;
uint8_t g_std_type_pos = 0;

uint8_t g_type_GPS = 0;
uint8_t g_type_WHEEL = 0;
uint8_t g_type_INS = 0;

uint8_t g_can0_rx_flag = 0;
uint8_t g_can1_rx_flag = 0;

uint8_t g_CAN_Count = 0;

CANDevTypeDef hCAN0 = {
	CAN0,RCU_CAN0,GPIOA,RCU_GPIOA,GPIO_PIN_11,GPIOA,RCU_GPIOA,GPIO_PIN_12,CAN0_RX0_IRQn,0,0,
	{	//parameter
		CAN_NORMAL_MODE,
		CAN_BT_SJW_1TQ,
		CAN_BT_BS1_7TQ,
		CAN_BT_BS2_2TQ,
		DISABLE,
		ENABLE,
		ENABLE,
		ENABLE,
		DISABLE,
		DISABLE,
		//5		1000K
		10		//500K
		//20	250K
		//40	125K
		//50	100K
		//100	50K
		//250	20K
	},
	{		//Filter
		0x0000,
		0x0000,
		0x0000,
		0x0000,
		CAN_FIFO0,
		0,
		CAN_FILTERMODE_MASK,
		CAN_FILTERBITS_32BIT,
		ENABLE
	},
	{		//Receive
		0,0,0,0,0,0,0,0,0,0
	},
	{		//Transmit
		0,0,0,0,0,0,0,0,0,0
	},
	RESET
};
CANDevTypeDef hCAN1 = {
	CAN1,RCU_CAN1,GPIOB,RCU_GPIOB,GPIO_PIN_13,GPIOB,RCU_GPIOB,GPIO_PIN_12,CAN1_RX0_IRQn,0,0,
	{		//parameter
		CAN_NORMAL_MODE,		//working_mode
		CAN_BT_SJW_1TQ,			//resync_jump_width
		CAN_BT_BS1_7TQ,			//time_segment_1
		CAN_BT_BS2_2TQ,			//time_segment_2
		DISABLE,				//time_triggered
		ENABLE,					//auto_bus_off_recovery
		ENABLE,					//auto_wake_up
		ENABLE,					//auto_retrans
		DISABLE,				//rec_fifo_overwrite
		DISABLE,				//trans_fifo_order
		//5		1000K
		10		//500K
		//20	250K
		//40	125K
		//50	100K
		//100	50K
		//250	20K
	},
	{		//Filter
		0x0000,					//filter_list_high
		0x0000,					//filter_list_low
		0x0000,					//filter_mask_high
		0x0000,					//filter_mask_low
		CAN_FIFO0,				//filter_fifo_number
		0,						//filter_number
		CAN_FILTERMODE_MASK,	//filter_mode
		CAN_FILTERBITS_32BIT,	//filter_bits
		ENABLE					//filter_enable
	},
	{		//Receive
		0,0,0,0,0,0,0,0,0,0
	},
	{		//Transmit
		0,0,0,0,0,0,0,0,0,0
	},
	RESET
};

void bsp_can_init(CANDevTypeDef* hCan)
{
	rcu_periph_clock_enable(hCan->canClk);
	/* initialize CAN register */
	can_deinit(hCan->canDev);

	hCan->CanPara.time_triggered = DISABLE;
	hCan->CanPara.auto_bus_off_recovery = ENABLE;
	hCan->CanPara.auto_wake_up = ENABLE;
	hCan->CanPara.auto_retrans = ENABLE;
	hCan->CanPara.rec_fifo_overwrite = DISABLE;
	hCan->CanPara.trans_fifo_order = DISABLE;
	hCan->CanPara.working_mode = CAN_NORMAL_MODE;
	hCan->CanPara.resync_jump_width = CAN_BT_SJW_1TQ;
	hCan->CanPara.time_segment_1 = CAN_BT_BS1_7TQ;
	hCan->CanPara.time_segment_2 = CAN_BT_BS2_2TQ;
	hCan->CanPara.prescaler = 10;
	
	can_init(hCan->canDev, &hCan->CanPara);
	
	hCan->CanFilter.filter_number = 0;
	hCan->CanFilter.filter_mode = CAN_FILTERMODE_MASK;
	hCan->CanFilter.filter_bits = CAN_FILTERBITS_32BIT;
	hCan->CanFilter.filter_list_high = 0x0000;
	hCan->CanFilter.filter_list_low = 0x0000;
	hCan->CanFilter.filter_mask_high = 0x0000;
	hCan->CanFilter.filter_mask_low = 0x0000;
	hCan->CanFilter.filter_fifo_number = CAN_FIFO0;
	hCan->CanFilter.filter_enable = ENABLE;
	
	can_filter_init(&hCan->CanFilter);
	
	rcu_periph_clock_enable(hCan->canHGPIO_CLK);
	rcu_periph_clock_enable(hCan->canLGPIO_CLK);
	/* configure CAN GPIO */
	gpio_output_options_set(hCan->canHGPIO, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, hCan->canH_Pin);
	gpio_mode_set(hCan->canHGPIO, GPIO_MODE_AF, GPIO_PUPD_PULLUP, hCan->canH_Pin);
	gpio_af_set(hCan->canHGPIO, GPIO_AF_9, hCan->canH_Pin);
	
	gpio_output_options_set(hCan->canLGPIO, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, hCan->canL_Pin);
	gpio_mode_set(hCan->canLGPIO, GPIO_MODE_AF, GPIO_PUPD_PULLUP, hCan->canL_Pin);
	gpio_af_set(hCan->canLGPIO, GPIO_AF_9, hCan->canL_Pin);
	
	/* configure CAN NVIC */
	nvic_irq_enable(hCan->canIRQ,hCan->prioMain,hCan->prioSub);
	
	/* enable CAN receive FIFO1 not empty interrupt */
	can_interrupt_enable(hCan->canDev, CAN_INT_RFNE0);
}

void bsp_can_transmit(CANDevTypeDef* hCan, can_trasnmit_message_struct* TxMsg)
{
	can_struct_para_init(CAN_TX_MESSAGE_STRUCT, &hCan->CanTxBuf);
	memcpy(&hCan->CanTxBuf,TxMsg,sizeof(can_trasnmit_message_struct));
	can_message_transmit(hCan->canDev, &hCan->CanTxBuf);
}

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV
void can_transmit_acc(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_efid = CAN_ID_INS_ACC;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSData.IMU_Data.ImuAcc_x / 0.0001220703125f);
	tx.tx_data[0] = (uint8_t)tD;
	tx.tx_data[1] = (uint8_t)(tD>>8);
	tD = (uint16_t)(hINSData.IMU_Data.ImuAcc_y / 0.0001220703125f);
	tx.tx_data[2] = (uint8_t)tD;
	tx.tx_data[3] = (uint8_t)(tD>>8);
	tD = (uint16_t)(hINSData.IMU_Data.ImuAcc_z / 0.0001220703125f);
	tx.tx_data[4] = (uint8_t)tD;
	tx.tx_data[5] = (uint8_t)(tD>>8);
	tx.tx_dlen = 6;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_gyro(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_efid = CAN_ID_INS_GYRO;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSData.IMU_Data.ImuGyro_x / 0.0076293f);
	tx.tx_data[0] = (uint8_t)tD;
	tx.tx_data[1] = (uint8_t)(tD>>8);
	tD = (uint16_t)(hINSData.IMU_Data.ImuGyro_y / 0.0076293f);
	tx.tx_data[2] = (uint8_t)tD;
	tx.tx_data[3] = (uint8_t)(tD>>8);
	tD = (uint16_t)(hINSData.IMU_Data.ImuGyro_z / 0.0076293f);
	tx.tx_data[4] = (uint8_t)tD;
	tx.tx_data[5] = (uint8_t)(tD>>8);
	tx.tx_dlen = 6;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_angle(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_efid = CAN_ID_INS_ANGLE;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSData.IMU_Data.ImuGyro_x / 0.010986f);
	tx.tx_data[0] = (uint8_t)tD;
	tx.tx_data[1] = (uint8_t)(tD>>8);
	tD = (uint16_t)(hINSData.IMU_Data.ImuGyro_y / 0.010986f);
	tx.tx_data[2] = (uint8_t)tD;
	tx.tx_data[3] = (uint8_t)(tD>>8);
	tD = (uint16_t)(hINSData.IMU_Data.ImuGyro_z / 0.010986f);
	tx.tx_data[4] = (uint8_t)tD;
	tx.tx_data[5] = (uint8_t)(tD>>8);
	tx.tx_dlen = 6;
	bsp_can_transmit(hCan,&tx);
}
#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV

//#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang
void can_transmit_acc(CANDevTypeDef* hCan) //ok
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_ACC;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)((hINSCANData.data_stream.accelX + 4) / 0.0001220703125);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)((hINSCANData.data_stream.accelY + 4) / 0.0001220703125);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)((hINSCANData.data_stream.accelZ + 4) / 0.0001220703125);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_gyro(CANDevTypeDef* hCan)	//ok
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_GYRO;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)((hINSCANData.data_stream.gyroX + 250.0) / 0.0076293f);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)((hINSCANData.data_stream.gyroY + 250.0) / 0.0076293f);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)((hINSCANData.data_stream.gyroZ + 250.0) / 0.0076293f);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_angle(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_ANGLE;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)((hINSCANData.data_stream.roll + 360.0) / 0.010986f);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)((hINSCANData.data_stream.pitch + 360.0) / 0.010986f);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)((hINSCANData.data_stream.azimuth + 360.0) / 0.010986f);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}
void can_transmit_pos(CANDevTypeDef* hCan) //ok
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_POSITION;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint32_t tD = (uint32_t)((hINSCANData.data_stream.latitude + 180.0) / 0.0000001f);
	tx.tx_data[0] = (uint8_t)(tD>>24);
	tx.tx_data[1] = (uint8_t)(tD>>16);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint32_t)((hINSCANData.data_stream.longitude + 180.0) / 0.0000001f);
	tx.tx_data[4] = (uint8_t)(tD>>24);
	tx.tx_data[5] = (uint8_t)(tD>>16);
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_h(CANDevTypeDef* hCan) //ok
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_HEIGHT;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint32_t tD = 0;
	tD = (uint32_t)((hINSCANData.data_stream.altitude + 10000) / 0.001);
	tx.tx_data[0] = (uint8_t)(tD>>24);
	tx.tx_data[1] = (uint8_t)(tD>>16);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint32_t)(hINSCANData.data_stream.poll_frame.gps_time);
	tx.tx_data[4] = (uint8_t)(tD>>24);
	tx.tx_data[5] = (uint8_t)(tD>>16);
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_speed(CANDevTypeDef* hCan)	//horspd 
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_SPEED;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)((hINSCANData.data_stream.speed_N + 100) / 0.0030517f);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)((hINSCANData.data_stream.speed_E + 100) / 0.0030517f);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)((hINSCANData.data_stream.speed_G + 100) / 0.0030517f);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_status(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_INFO;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = 0;
	tD = (uint8_t)hINSCANData.data_stream.GpsFlag_Pos;
	memset(tx.tx_data,0,8);
	
	tx.tx_data[0] = (uint8_t)tD;

	tD = (uint8_t)hINSCANData.data_stream.NumSV;
	tx.tx_data[1] = (uint8_t)tD;

	tD = (uint8_t)hINSCANData.data_stream.GpsFlag_heading;
	tx.tx_data[2] = (uint8_t)tD;

	tD = (uint8_t)hINSCANData.data_stream.Gps_Age;
	tx.tx_data[3] = (uint8_t)tD;

	tD = (uint8_t)hINSCANData.data_stream.Car_Status;
	tx.tx_data[4] = (uint8_t)tD;

	tD = (uint8_t)hINSCANData.data_stream.INS_status;
	tx.tx_data[5] = (uint8_t)tD;
	
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
	tx.tx_dlen = 8;
	if((g_type_GPS == 1)&&(g_type_WHEEL == 1)&&(g_type_INS == 1))
	{
		g_type_GPS = 0;
		g_type_WHEEL = 0;
		g_type_INS = 0;
		bsp_can_transmit(hCan,&tx);
	}
}

void can_transmit_std0(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_STD0;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = 0;
	double t = 0;
	tx.tx_dlen = 8;
	if(( g_std_type_pos == 1) &&(g_std_type_angle == 1))
	{
		g_std_type_pos = 0;
		g_std_type_angle = 0;
		bsp_can_transmit(hCan,&tx);
	}
}


void can_transmit_std1(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_STD1;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint32_t tD = 0;
	tD = (uint32_t)hINSCANData.data_stream.gps_week;
	tx.tx_data[4] = (uint8_t)(tD>>24);
	tx.tx_data[5] = (uint8_t)(tD>>16);
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_posatemp(CANDevTypeDef* hCan) //device temperature
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_POSaTEMP;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSCANData.data_stream.latitude / 0.01);
	memset(tx.tx_data,0,8);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.longitude / 0.01);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	
	tD = (uint16_t)(hINSCANData.data_stream.devicetemperature / 0.01);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 6;
	bsp_can_transmit(hCan,&tx);
}


#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

void can_transmit_acc(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_ACC;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSCANData.data_stream.accelX / 0.00012207f);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.accelY / 0.00012207f);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.accelZ / 0.00012207f);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_gyro(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_GYRO;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSCANData.data_stream.gyroX / 0.0076293f);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.gyroY / 0.0076293f);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.gyroZ / 0.0076293f);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_angle(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_ANGLE;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSCANData.data_stream.roll / 0.010986f);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.pitch / 0.010986f);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.azimuth / 0.010986f);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}
void can_transmit_pos(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_POSITION;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint32_t tD = (uint32_t)(hINSCANData.data_stream.latitude / 0.0000001f);
	tx.tx_data[0] = (uint8_t)(tD>>24);
	tx.tx_data[1] = (uint8_t)(tD>>16);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint32_t)(hINSCANData.data_stream.longitude / 0.0000001f);
	tx.tx_data[4] = (uint8_t)(tD>>24);
	tx.tx_data[5] = (uint8_t)(tD>>16);
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}
void can_transmit_speed(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_SPEED;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSCANData.data_stream.speed_N / 0.0030517f);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.speed_G / 0.0030517f);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.speed_E / 0.0030517f);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_status(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_INFO;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = 0;
	switch(hINSCANData.data_stream.poll_frame.type)
	{
		case 32:
			g_type_GPS = 1;
			tx.tx_data[0] = (uint8_t)hINSCANData.data_stream.poll_frame.data1;
			tx.tx_data[1] = (uint8_t)hINSCANData.data_stream.poll_frame.data2;
			tx.tx_data[2] = (uint8_t)hINSCANData.data_stream.poll_frame.data3;
			break;
		case 33:
			g_type_WHEEL = 1;
//			tx.tx_data[3] = (uint8_t) 1;
			tx.tx_data[4] = (uint8_t)hINSCANData.data_stream.poll_frame.data2;
			break;
		case 36:
			g_type_INS = 1;
			tx.tx_data[7] = (uint8_t)hINSCANData.data_stream.poll_frame.data1;
			break;
		default:
			break;
	}
	tx.tx_dlen = 8;
	if((g_type_GPS == 1)&&(g_type_WHEEL == 1)&&(g_type_INS == 1))
	{
		g_type_GPS = 0;
		g_type_WHEEL = 0;
		g_type_INS = 0;
		if(time_base_100ms_Flag == 1)
			bsp_can_transmit(hCan,&tx);
	}
}

void can_transmit_std(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_STD;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = 0;
	double t = 0;
	switch(hINSCANData.data_stream.poll_frame.type)
	{
		case 0:
			g_std_type_pos = 1;
			t = pow(2.718281,hINSCANData.data_stream.poll_frame.data1/100);
			tD = (uint16_t)( t );
			tx.tx_data[0] = (uint8_t)(tD>>8);
			tx.tx_data[1] = (uint8_t)tD;
			t = pow(2.718281,hINSCANData.data_stream.poll_frame.data2/100);
			tD = (uint16_t)( t );
			tx.tx_data[2] = (uint8_t)(tD>>8);
			tx.tx_data[3] = (uint8_t)tD;
			t = pow(2.718281,hINSCANData.data_stream.poll_frame.data3/100);
			tD = (uint16_t)( t );
			tx.tx_data[4] = (uint8_t)(tD>>8);
			tx.tx_data[5] = (uint8_t)tD;
			
			break;
		case 2:
			g_std_type_angle = 1;
			t = pow(2.718281,hINSCANData.data_stream.poll_frame.data3/100);
			tD = (uint16_t)( t / 0.01 );
			tx.tx_data[6] = (uint8_t)(tD>>8);
			tx.tx_data[7] = (uint8_t)tD;
			break;
		default:
			break;
	}
	tx.tx_dlen = 8;
	if(( g_std_type_pos == 1) &&(g_std_type_angle == 1))
	{
		g_std_type_pos = 0;
		g_std_type_angle = 0;
		if(time_base_100ms_Flag == 1)
			bsp_can_transmit(hCan,&tx);
	}
}

void can_transmit_h(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_HEIGHT;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint32_t tD = 0;
	tD = (uint32_t)(hINSCANData.data_stream.altitude / 0.001);
	tx.tx_data[0] = (uint8_t)(tD>>24);
	tx.tx_data[1] = (uint8_t)(tD>>16);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint32_t)(hINSCANData.data_stream.poll_frame.gps_time );
	tx.tx_data[4] = (uint8_t)(tD>>24);
	tx.tx_data[5] = (uint8_t)(tD>>16);
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

//void can_transmit_gpsweek(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_POSITION;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;
//	uint32_t tD = 0;
//	tD = (uint32_t)hINSCANData.data_stream.gps_week;
//	tx.tx_data[4] = (uint8_t)(tD>>24);
//	tx.tx_data[5] = (uint8_t)(tD>>16);
//	tx.tx_data[6] = (uint8_t)(tD>>8);
//	tx.tx_data[7] = (uint8_t)tD;
//	tx.tx_dlen = 8;
//	bsp_can_transmit(hCan,&tx);
//}

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

//#if VEHICLE_TYPE_USED == VEHICLE_TYPE_AutoBots_RoboMix
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD
can_trasnmit_message_struct status_tx;
can_trasnmit_message_struct std_tx;

void can_transmit_acc(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_ACC;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)hINSCANData.data_stream.accelX;
	memset(tx.tx_data,0x0,8);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.accelY;
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.accelZ;
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 6;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_gyro(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_GYRO;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)hINSCANData.data_stream.gyroX;
	memset(tx.tx_data,0x0,8);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.gyroY;
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.gyroZ;
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 6;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_angle(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_ANGLE;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSCANData.data_stream.roll);
	memset(tx.tx_data,0x0,8);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.pitch);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.azimuth);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 6;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_h(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_HEIGHT;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint32_t tD = 0;
	tD = (uint32_t)(hINSCANData.data_stream.altitude);
	tx.tx_data[0] = (uint8_t)(tD>>24);
	tx.tx_data[1] = (uint8_t)(tD>>16);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint32_t)(hINSCANData.data_stream.gpssecond);
	tx.tx_data[4] = (uint8_t)(tD>>24);
	tx.tx_data[5] = (uint8_t)(tD>>16);
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_pos(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_POSITION;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint32_t tD = (uint32_t)(hINSCANData.data_stream.latitude);
	memset(tx.tx_data,0,8);
	tx.tx_data[0] = (uint8_t)(tD>>24);
	tx.tx_data[1] = (uint8_t)(tD>>16);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint32_t)(hINSCANData.data_stream.longitude);
	tx.tx_data[4] = (uint8_t)(tD>>24);
	tx.tx_data[5] = (uint8_t)(tD>>16);
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_speed(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_SPEED;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)hINSCANData.data_stream.vn;
	memset(tx.tx_data,0,8);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.ve;
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.vu;
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 6;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_status(CANDevTypeDef* hCan)	// xxx
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_STATUS;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	
	memset(tx.tx_data,0,8);
	
	uint16_t tD = (uint8_t)hINSCANData.data_stream.GpsFlag_Pos;
	tx.tx_data[0] = (uint8_t)tD;

	tD = (uint8_t)hINSCANData.data_stream.NumSV;
	tx.tx_data[1] = (uint8_t)tD;

	tD = (uint8_t)hINSCANData.data_stream.GpsFlag_heading;
	tx.tx_data[2] = (uint8_t)tD;

	tD = (uint8_t)hINSCANData.data_stream.Gps_Age;
	tx.tx_data[3] = (uint8_t)tD;

	
	tD = (uint8_t)hINSCANData.data_stream.Car_Status;
	tx.tx_data[4] = (uint8_t)tD;

	tD = (uint16_t)hINSCANData.data_stream.gps_week;
	tx.tx_data[5] = (uint8_t)(tD>>8);
	tx.tx_data[6] = (uint8_t)tD;

	tD = (uint8_t)hINSCANData.data_stream.INS_status;
	tx.tx_data[7] = (uint8_t)tD;
	
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}


void can_transmit_std_heading(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_STD_HEADING;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;

	memset(tx.tx_data,0,8);
	
	uint16_t tD = (uint16_t)hINSCANData.data_stream.std_lat;
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;

	tD = (uint16_t)hINSCANData.data_stream.std_lon;
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;

	tD = (uint16_t)hINSCANData.data_stream.std_height;
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;

	tD = (uint16_t)hINSCANData.data_stream.std_heading;
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
		
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

//void can_transmit_std_speed(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_STD_SPEED;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;

//	memset(tx.tx_data,0,8);
//	
//	uint16_t tD = (uint16_t)hINSCANData.data_stream.std_ve;
//	tx.tx_data[0] = (uint8_t)(tD>>8);
//	tx.tx_data[1] = (uint8_t)tD;

//	tD = (uint16_t)hINSCANData.data_stream.std_vn;
//	tx.tx_data[2] = (uint8_t)(tD>>8);
//	tx.tx_data[3] = (uint8_t)tD;

//	tD = (uint16_t)hINSCANData.data_stream.std_vu;
//	tx.tx_data[4] = (uint8_t)(tD>>8);
//	tx.tx_data[5] = (uint8_t)tD;

//	tx.tx_dlen = 6;
//	bsp_can_transmit(hCan,&tx);
//}

void can_transmit_temperature(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_TEMPERATURE;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;

	memset(tx.tx_data,0,8);
	
	uint16_t tD = (uint16_t)hINSCANData.data_stream.std_roll;
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;

	tD = (uint16_t)hINSCANData.data_stream.std_pitch;
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;

	tD = (uint16_t)hINSCANData.data_stream.devicetemperature;
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;

	tx.tx_dlen = 6;
	bsp_can_transmit(hCan,&tx);
}


//void can_transmit_gpstime(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_GPSTIME;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;
//	gtime_t m_gpstime = gpst2time(hINSCANData.data_stream.gps_week,hINSCANData.data_stream.gpssecond/1000.0);
//	gtime_t m_utctime = gpst2utc(m_gpstime);
//	double ep[6]={0};
//	double m_ms=0;
//	time2epoch(m_gpstime,ep);
//	memset(tx.tx_data,0,8);

//	uint8_t tD = (uint8_t)(ep[0]-2000);
//	tx.tx_data[0] = (uint8_t)tD;

//	tD = (uint8_t)ep[1];
//	tx.tx_data[1] = (uint8_t)tD;

//	tD = (uint8_t)ep[2];
//	tx.tx_data[2] = (uint8_t)tD;

//	tD = (uint8_t)ep[3];
//	tx.tx_data[3] = (uint8_t)tD;

//	tD = (uint8_t)ep[4];
//	tx.tx_data[4] = (uint8_t)tD;

//	tD = (uint8_t)ep[5];
//	tx.tx_data[5] = (uint8_t)tD;

//	m_ms=1000*(ep[5]-(uint8_t)ep[5]);
//	tD = (uint8_t)m_ms;
//	tx.tx_data[6] = (uint8_t)tD;

//	tx.tx_dlen = 8;
//	bsp_can_transmit(hCan,&tx);
//	
//}

//void can_transmit_posatemp(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_POSaTEMP;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;
//	uint32_t tD = (uint32_t)(hINSCANData.data_stream.latitude);
//	memset(tx.tx_data,0,8);
//	tx.tx_data[0] = (uint8_t)(tD>>24);
//	tx.tx_data[1] = (uint8_t)(tD>>16);
//	tx.tx_data[2] = (uint8_t)(tD>>8);
//	tx.tx_data[3] = (uint8_t)tD;
//	tD = (uint32_t)(hINSCANData.data_stream.longitude);
//	tx.tx_data[4] = (uint8_t)(tD>>24);
//	tx.tx_data[5] = (uint8_t)(tD>>16);
//	tx.tx_data[6] = (uint8_t)(tD>>8);
//	tx.tx_data[7] = (uint8_t)tD;
//	tx.tx_dlen = 8;
//	bsp_can_transmit(hCan,&tx);
//}

//IMU标定状态
//void can_transmit_cali(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_CALI;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;
//	uint16_t tD =0;
//	memset(tx.tx_data,0,8);
//	if(2 == hINSCANData.data_stream.Nav_Standard_flag) {
//		tD=0;
//	}
//	else {
//		tD=1;
//	}

//	tx.tx_data[0] = (uint8_t)(tD>>8);
//	tx.tx_data[1] = (uint8_t)tD;

//	tx.tx_dlen = 8;
//	bsp_can_transmit(hCan,&tx);
//}

//#define CAN_ID_INS_SYS_STATUS				0x030//INS系统状态
//void can_transmit_sys_status(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_SYS_STATUS;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;
//	uint32_t tD = (uint32_t)(hINSCANData.data_stream.INS_States);
//	memset(tx.tx_data,0,8);
//	tx.tx_data[0] = (uint8_t)(tD>>24);
//	tx.tx_data[1] = (uint8_t)(tD>>16);
//	tx.tx_data[2] = (uint8_t)(tD>>8);
//	tx.tx_data[3] = (uint8_t)tD;

//	tx.tx_dlen = 8;
//	bsp_can_transmit(hCan,&tx);
//}

//#define CAN_ID_INS_GEAR				0x110  //档位
//void can_transmit_gear(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_GEAR;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;
//	uint8_t tD =0;
//	memset(tx.tx_data,0,8);
//	if(2==hINSCANData.data_stream.Gear) {
//		tD = 4;
//	}
//	else if(0==hINSCANData.data_stream.Gear) {
//		tD = 3;
//	}
//	else if(4==hINSCANData.data_stream.Gear) {
//		tD = 2;
//	}
//	else {
//		tD = 4;
//	}
//	tx.tx_data[0] = (uint8_t)tD;

//	tx.tx_dlen = 8;
//	bsp_can_transmit(hCan,&tx);
//}
//#define CAN_ID_INS_VEL_SPEED			0x010 //车辆速度
//void can_transmit_vel_speed(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_VEL_SPEED;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;
//	uint16_t tD = (uint16_t)hINSCANData.data_stream.WheelSpeed;
//	memset(tx.tx_data,0,8);
//	tx.tx_data[0] = (uint8_t)(tD>>8);
//	tx.tx_data[1] = (uint8_t)tD;

//	tx.tx_dlen = 8;
//	bsp_can_transmit(hCan,&tx);
//}

//void can_transmit_status(CANDevTypeDef* hCan)
//{
//	bsp_can_transmit(hCan,&status_tx);
//}
//void can_fill_status(CANDevTypeDef* hCan)
//{
//	status_tx.tx_sfid = CAN_ID_INS_INFO;
//	status_tx.tx_ff = CAN_FF_STANDARD;
//	status_tx.tx_ft = CAN_FT_DATA;
//	uint16_t tD = 0;
//	switch(hINSCANData.data_stream.type)
//	{
//		case 32:
//			g_type_GPS = 1;
//			status_tx.tx_data[0] = 0;//(uint8_t)hINSCANData.data_stream.poll_frame.data1;
//			status_tx.tx_data[1] = (uint8_t)hINSCANData.data_stream.poll_frame.data2;
//			status_tx.tx_data[2] = 0;//(uint8_t)hINSCANData.data_stream.poll_frame.data3;
//			break;
////		case 33:
////			g_type_WHEEL = 1;
//////			tx.tx_data[3] = (uint8_t) 1;
////			status_tx.tx_data[4] = (uint8_t)hINSCANData.data_stream.poll_frame.data2;
////			break;
////		case 36:
////			g_type_INS = 1;
////			status_tx.tx_data[7] = (uint8_t)hINSCANData.data_stream.poll_frame.data1;
////			break;
//		default:
//			break;
//	}
//	status_tx.tx_dlen = 8;
//}
//#if 0
//void can_transmit_std(CANDevTypeDef* hCan)
//{
//	bsp_can_transmit(hCan,&std_tx);
//}
//#endif

//void can_fill_std(CANDevTypeDef* hCan)
//{
//	std_tx.tx_sfid = CAN_ID_INS_STD;
//	std_tx.tx_ff = CAN_FF_STANDARD;
//	std_tx.tx_ft = CAN_FT_DATA;
//	uint16_t tD = 0;
//	double t = 0;
//	switch(hINSCANData.data_stream.type)
//	{
//		case 0:
//			g_std_type_pos = 1;
//			tD = (uint16_t)hINSCANData.data_stream.poll_frame.data1;
//			std_tx.tx_data[0] = (uint8_t)(tD>>8);
//			std_tx.tx_data[1] = (uint8_t)tD;
//			tD = (uint16_t)hINSCANData.data_stream.poll_frame.data2;
//			std_tx.tx_data[2] = (uint8_t)(tD>>8);
//			std_tx.tx_data[3] = (uint8_t)tD;
//			tD = (uint16_t)hINSCANData.data_stream.poll_frame.data3;
//			std_tx.tx_data[4] = (uint8_t)(tD>>8);
//			std_tx.tx_data[5] = (uint8_t)tD;
//			break;
//		case 2:
//			g_std_type_angle = 1;
//			tD = (uint16_t)hINSCANData.data_stream.poll_frame.data3;
//			std_tx.tx_data[6] = (uint8_t)(tD>>8);
//			std_tx.tx_data[7] = (uint8_t)tD;
//			break;
//		default:
//			break;
//	}
//	std_tx.tx_dlen = 8;
//}


//void can_transmit_gpsweek(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_POSITION;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;
//	uint32_t tD = 0;
//	tD = (uint32_t)hINSCANData.data_stream.gps_week;
//	tx.tx_data[4] = (uint8_t)(tD>>24);
//	tx.tx_data[5] = (uint8_t)(tD>>16);
//	tx.tx_data[6] = (uint8_t)(tD>>8);
//	tx.tx_data[7] = (uint8_t)tD;
//	tx.tx_dlen = 8;
//	bsp_can_transmit(hCan,&tx);
//}
#endif 

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70

can_trasnmit_message_struct status_tx;
can_trasnmit_message_struct std_tx;

void can_transmit_acc(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_ACC;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)hINSCANData.data_stream.accelX;
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.accelY;
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.accelZ;
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_gyro(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_GYRO;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)hINSCANData.data_stream.gyroX;
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.gyroY;
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.gyroZ;
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_angle(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_ANGLE;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)(hINSCANData.data_stream.roll / 0.010986f);
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.pitch / 0.010986f);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)(hINSCANData.data_stream.azimuth / 0.010986f);
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}
void can_transmit_pos(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_POSITION;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint32_t tD = (uint32_t)(hINSCANData.data_stream.latitude);
	tx.tx_data[0] = (uint8_t)(tD>>24);
	tx.tx_data[1] = (uint8_t)(tD>>16);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint32_t)(hINSCANData.data_stream.longitude);
	tx.tx_data[4] = (uint8_t)(tD>>24);
	tx.tx_data[5] = (uint8_t)(tD>>16);
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}
void can_transmit_speed(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_SPEED;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = (uint16_t)hINSCANData.data_stream.vn;
	tx.tx_data[0] = (uint8_t)(tD>>8);
	tx.tx_data[1] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.vu;
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint16_t)hINSCANData.data_stream.ve;
	tx.tx_data[4] = (uint8_t)(tD>>8);
	tx.tx_data[5] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

void can_transmit_status(CANDevTypeDef* hCan)
{
	bsp_can_transmit(hCan,&status_tx);
}
void can_fill_status(CANDevTypeDef* hCan)
{
	status_tx.tx_sfid = CAN_ID_INS_INFO;
	status_tx.tx_ff = CAN_FF_STANDARD;
	status_tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = 0;
	switch(hINSCANData.data_stream.type)
	{
		case 32:
			g_type_GPS = 1;
			status_tx.tx_data[0] = (uint8_t)hINSCANData.data_stream.poll_frame.data1;
			status_tx.tx_data[1] = (uint8_t)hINSCANData.data_stream.poll_frame.data2;
			status_tx.tx_data[2] = (uint8_t)hINSCANData.data_stream.poll_frame.data3;
			break;
		case 33:
			g_type_WHEEL = 1;
//			tx.tx_data[3] = (uint8_t) 1;
			status_tx.tx_data[4] = (uint8_t)hINSCANData.data_stream.poll_frame.data2;
			break;
		case 36:
			g_type_INS = 1;
			status_tx.tx_data[7] = (uint8_t)hINSCANData.data_stream.poll_frame.data1;
			break;
		default:
			break;
	}
	status_tx.tx_dlen = 8;
}

void can_transmit_std(CANDevTypeDef* hCan)
{
	bsp_can_transmit(hCan,&std_tx);
}

void can_fill_std(CANDevTypeDef* hCan)
{
	std_tx.tx_sfid = CAN_ID_INS_STD;
	std_tx.tx_ff = CAN_FF_STANDARD;
	std_tx.tx_ft = CAN_FT_DATA;
	uint16_t tD = 0;
	double t = 0;
	switch(hINSCANData.data_stream.type)
	{
		case 0:
			g_std_type_pos = 1;
			tD = (uint16_t)hINSCANData.data_stream.poll_frame.data1;
			std_tx.tx_data[0] = (uint8_t)(tD>>8);
			std_tx.tx_data[1] = (uint8_t)tD;
			tD = (uint16_t)hINSCANData.data_stream.poll_frame.data2;
			std_tx.tx_data[2] = (uint8_t)(tD>>8);
			std_tx.tx_data[3] = (uint8_t)tD;
			tD = (uint16_t)hINSCANData.data_stream.poll_frame.data3;
			std_tx.tx_data[4] = (uint8_t)(tD>>8);
			std_tx.tx_data[5] = (uint8_t)tD;
			
			break;
		case 2:
			g_std_type_angle = 1;
			tD = (uint16_t)hINSCANData.data_stream.poll_frame.data3;
			std_tx.tx_data[6] = (uint8_t)(tD>>8);
			std_tx.tx_data[7] = (uint8_t)tD;
			break;
		default:
			break;
	}
	std_tx.tx_dlen = 8;
}

void can_transmit_h(CANDevTypeDef* hCan)
{
	can_trasnmit_message_struct tx;
	tx.tx_sfid = CAN_ID_INS_HEIGHT;
	tx.tx_ff = CAN_FF_STANDARD;
	tx.tx_ft = CAN_FT_DATA;
	uint32_t tD = 0;
	tD = (uint32_t)(hINSCANData.data_stream.altitude);
	tx.tx_data[0] = (uint8_t)(tD>>24);
	tx.tx_data[1] = (uint8_t)(tD>>16);
	tx.tx_data[2] = (uint8_t)(tD>>8);
	tx.tx_data[3] = (uint8_t)tD;
	tD = (uint32_t)(hINSCANData.data_stream.gps_time );
	tx.tx_data[4] = (uint8_t)(tD>>24);
	tx.tx_data[5] = (uint8_t)(tD>>16);
	tx.tx_data[6] = (uint8_t)(tD>>8);
	tx.tx_data[7] = (uint8_t)tD;
	tx.tx_dlen = 8;
	bsp_can_transmit(hCan,&tx);
}

//void can_transmit_gpsweek(CANDevTypeDef* hCan)
//{
//	can_trasnmit_message_struct tx;
//	tx.tx_sfid = CAN_ID_INS_POSITION;
//	tx.tx_ff = CAN_FF_STANDARD;
//	tx.tx_ft = CAN_FT_DATA;
//	uint32_t tD = 0;
//	tD = (uint32_t)hINSCANData.data_stream.gps_week;
//	tx.tx_data[4] = (uint8_t)(tD>>24);
//	tx.tx_data[5] = (uint8_t)(tD>>16);
//	tx.tx_data[6] = (uint8_t)(tD>>8);
//	tx.tx_data[7] = (uint8_t)tD;
//	tx.tx_dlen = 8;
//	bsp_can_transmit(hCan,&tx);
//}


#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70

double range(double * pData, uint32_t nSize)
{
	double max = *pData, min = *pData;
	double * p = pData ++;
	int i = 1, j = 1;
	for(i = 1; i < nSize; i ++)
	{
		if(max < *p)
			max = *p;
		p ++ ;
	}
	p = pData ++;
	for(j = 1; j < nSize; j ++)
	{
		if(min < *p)
			min = *p;
		p ++ ;
	}
	return (max - min);
}

double variance(double * pData, uint32_t nSize)
{
	int i;
	double sum,aver,x,y;
	for(i=0;i<nSize;i++)
	{
		sum+=pData[i];
		aver=sum/nSize;
	}
	for(i=0;i<nSize;i++)
	{
		y=pData[i]-aver;
		x+=y*y;
	}
	x = x/(nSize-1);
	return x;
}

double stdDev(double * pData, uint32_t nSize)
{
	int i;
	double sum,aver,x,y;
	for(i=0;i<nSize;i++)
	{
		sum+=pData[i];
		aver=sum/nSize;
	}
	for(i=0;i<nSize;i++)
	{
		y=pData[i]-aver;
		x+=y*y;
	}
	x = x/(nSize-1);
	return sqrt(x);
}

extern	int gbilldebuguart4;
extern	int gcan0_rx_syn;
void uart4sendmsg_canout(can_receive_message_struct *receive_message)
{
	char canrxbuf[200];
	if (gcan0_rx_syn == 0) return;
	gcan0_rx_syn = 0;
	//sprintf(canrxbuf, "-%03d.%03d  %03d.%03d.%03d.%03d  %03d.%03d.%03d.%03d - %03d.%03d.%03d.%03d\r\n", receive_message->rx_sfid,  receive_message->rx_efid, receive_message->rx_ff, receive_message->rx_ft, receive_message->rx_dlen, receive_message->rx_fi,
	sprintf(canrxbuf, "-0x%04x.%03d.%03d  %02x.%02x.%02x.%02x  %02x.%02x.%02x.%02x - %02x.%02x.%02x.%02x\r\n", 
			receive_message->rx_sfid, receive_message->rx_sfid, receive_message->rx_efid, 
			receive_message->rx_ff, receive_message->rx_ft, receive_message->rx_dlen, receive_message->rx_fi,
			receive_message->rx_data[0], receive_message->rx_data[1], receive_message->rx_data[2], receive_message->rx_data[3],
			receive_message->rx_data[4], receive_message->rx_data[5], receive_message->rx_data[6], receive_message->rx_data[7]);

	gbilldebuguart4 = 0;
	uart4sendmsg(canrxbuf, strlen(canrxbuf));
	gbilldebuguart4 = 1;
}
/*!
    \brief      this function handles CAN0 RX0 exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
can_receive_message_struct gCanRxBuf;
void CAN0_RX0_IRQHandler(void)
{
	/* check the receive message */
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV
	can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);
	gcan0_rx_syn = 1;
	memcpy(&gCanRxBuf, &hCAN0.CanRxBuf, sizeof(gCanRxBuf));
	if((CAN_ID_GEAR == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft)){
		hCAN0.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = hCAN0.CanRxBuf.rx_data[4] & 0x07;
		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
		combineData.canInfo.data.Gear = hINSData.CAN_Data.Gear; 			
		combineData.canInfo.counter++;			
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_FRONT_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//前轮轮速
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Right = (((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN0.CanRxBuf.rx_data[2]>>7))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)(hCAN0.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN0.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN0.CanRxBuf.rx_data[4]>>6))*0.01f;
		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
		combineData.canInfo.data.WheelSpeed_Front_Left = hINSData.CAN_Data.WheelSpeed_Front_Left; 
		combineData.canInfo.data.WheelSpeed_Front_Right = hINSData.CAN_Data.WheelSpeed_Front_Right; 
		combineData.canInfo.counter++;			
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_BACK_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//后轮轮速
		g_back_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Back_Right = (((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN0.CanRxBuf.rx_data[2]>>7))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Back_Left = (((uint16_t)(hCAN0.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN0.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN0.CanRxBuf.rx_data[4]>>6))*0.01f;
		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
		combineData.canInfo.data.WheelSpeed_Back_Left = hINSData.CAN_Data.WheelSpeed_Back_Left; 
		combineData.canInfo.data.WheelSpeed_Back_Right = hINSData.CAN_Data.WheelSpeed_Back_Right; 
		combineData.canInfo.counter++;			
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WHEEL_STEER == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//方向盘
		hINSData.CAN_Data.WheelSteer = 0;//hCAN0.CanRxBuf.rx_data[0]*180.0f/65536.0f;
		combineData.canInfo.data.WheelSteer = hINSData.CAN_Data.WheelSteer; 
		combineData.canInfo.counter++;			
	}
	else if((CAN_ID_ODOMETER == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//里程计
		hINSData.CAN_Data.OdoPulse_1 = 0;//hCAN0.CanRxBuf.rx_data[0]*0.00863f;
		hINSData.CAN_Data.OdoPulse_2 = 0;//hCAN0.CanRxBuf.rx_data[0]*0.00863f;
		combineData.canInfo.data.OdoPulse_1 = hINSData.CAN_Data.OdoPulse_1; 
		combineData.canInfo.data.OdoPulse_2 = hINSData.CAN_Data.OdoPulse_2; 
		combineData.canInfo.counter++;	
	}
	else if((CAN_ID_OTA == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}
#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV
	
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD
	can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);
	gcan0_rx_syn = 1;
	memcpy(&gCanRxBuf, &hCAN0.CanRxBuf, sizeof(gCanRxBuf));

#if 1	//wu ling 
	if((CAN_ID_WL_GEAR == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft)){
		hCAN0.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = hCAN0.CanRxBuf.rx_data[4] & 0x07;
		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
		combineData.canInfo.data.Gear = hINSData.CAN_Data.Gear; 			
		combineData.canInfo.counter++;			
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WL_FRONT_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//前轮轮速
		g_front_detected = 1;
//		hINSData.CAN_Data.WheelSpeed_Front_Right = (((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN0.CanRxBuf.rx_data[2]>>7))*0.01f;
//		hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)(hCAN0.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN0.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN0.CanRxBuf.rx_data[4]>>6))*0.01f;
		
				hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN0.CanRxBuf.rx_data[2]>>7))*0.01f;
		    hINSData.CAN_Data.WheelSpeed_Front_Right = (((uint16_t)(hCAN0.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN0.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN0.CanRxBuf.rx_data[4]>>6))*0.01f;
		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
		combineData.canInfo.data.WheelSpeed_Front_Left = hINSData.CAN_Data.WheelSpeed_Front_Left; 
		combineData.canInfo.data.WheelSpeed_Front_Right = hINSData.CAN_Data.WheelSpeed_Front_Right; 
		combineData.canInfo.counter++;			
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WL_BACK_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//后轮轮速
		g_back_detected = 1;
//		hINSData.CAN_Data.WheelSpeed_Back_Right = (((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN0.CanRxBuf.rx_data[2]>>7))*0.01f;
//		hINSData.CAN_Data.WheelSpeed_Back_Left = (((uint16_t)(hCAN0.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN0.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN0.CanRxBuf.rx_data[4]>>6))*0.01f;
				hINSData.CAN_Data.WheelSpeed_Back_Left = (((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN0.CanRxBuf.rx_data[2]>>7))*0.01f;
		    hINSData.CAN_Data.WheelSpeed_Back_Right = (((uint16_t)(hCAN0.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN0.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN0.CanRxBuf.rx_data[4]>>6))*0.01f;
		
		
		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
		combineData.canInfo.data.WheelSpeed_Back_Left = hINSData.CAN_Data.WheelSpeed_Back_Left; 
		combineData.canInfo.data.WheelSpeed_Back_Right = hINSData.CAN_Data.WheelSpeed_Back_Right; 
		combineData.canInfo.counter++;			
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
#else //BYD
	if((CAN_ID_BYD_GEAR == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft)){
		hCAN0.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = hCAN0.CanRxBuf.rx_data[3] & 0x0f;
		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
		combineData.canInfo.data.Gear = hINSData.CAN_Data.Gear; 	
		if (combineData.canInfo.data.Gear == 4)		combineData.canInfo.data.Gear = 2;
		else if (combineData.canInfo.data.Gear == 2)		combineData.canInfo.data.Gear = 4;
		else if (combineData.canInfo.data.Gear == 3)		combineData.canInfo.data.Gear = 0;
		else if (combineData.canInfo.data.Gear == 1)		combineData.canInfo.data.Gear = 0;
		else combineData.canInfo.data.Gear = 0;
		combineData.canInfo.counter++;			
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_BYD_FRONT_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//前轮轮速
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Right = ((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x3F)<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1])*0.008868f * 3.6;
		hINSData.CAN_Data.WheelSpeed_Front_Left =  ((uint16_t)((hCAN0.CanRxBuf.rx_data[2]&0x3F)<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3])*0.008868f * 3.6;
		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
		combineData.canInfo.data.WheelSpeed_Front_Left = hINSData.CAN_Data.WheelSpeed_Front_Left; 
		combineData.canInfo.data.WheelSpeed_Front_Right = hINSData.CAN_Data.WheelSpeed_Front_Right; 
		combineData.canInfo.counter++;			
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_BYD_BACK_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//后轮轮速
		g_back_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Back_Right = ((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x3F)<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1])*0.008868f * 3.6;
		hINSData.CAN_Data.WheelSpeed_Back_Left =  ((uint16_t)((hCAN0.CanRxBuf.rx_data[2]&0x3F)<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3])*0.008868f * 3.6;
		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
		combineData.canInfo.data.WheelSpeed_Back_Left = hINSData.CAN_Data.WheelSpeed_Back_Left; 
		combineData.canInfo.data.WheelSpeed_Back_Right = hINSData.CAN_Data.WheelSpeed_Back_Right; 
		combineData.canInfo.counter++;			
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
#endif
	else if((CAN_ID_WHEEL_STEER == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//方向盘
		hINSData.CAN_Data.WheelSteer = 0;//hCAN0.CanRxBuf.rx_data[0]*180.0f/65536.0f;
		combineData.canInfo.data.WheelSteer = hINSData.CAN_Data.WheelSteer; 
		combineData.canInfo.counter++;			
	}
	else if((CAN_ID_ODOMETER == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//里程计
		hINSData.CAN_Data.OdoPulse_1 = 0;//hCAN0.CanRxBuf.rx_data[0]*0.00863f;
		hINSData.CAN_Data.OdoPulse_2 = 0;//hCAN0.CanRxBuf.rx_data[0]*0.00863f;
		combineData.canInfo.data.OdoPulse_1 = hINSData.CAN_Data.OdoPulse_1; 
		combineData.canInfo.data.OdoPulse_2 = hINSData.CAN_Data.OdoPulse_2; 
		combineData.canInfo.counter++;	
	}
	else if((CAN_ID_OTA == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}
	
	#if c_systemrunmode_cantooling
	else if((CAN_ID_INS_ACC == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;
		grs422_frameD.accelX = ((uint16_t)(hCAN0.CanRxBuf.rx_data[0]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1]) * 0.00018310546875 - 6.0;
		grs422_frameD.accelY = ((uint16_t)(hCAN0.CanRxBuf.rx_data[2]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3]) * 0.00018310546875 - 6.0;
		grs422_frameD.accelZ = ((uint16_t)(hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]) * 0.00018310546875 - 6.0;
		grs422_frame.data_stream.accelX = ((uint16_t)(hCAN0.CanRxBuf.rx_data[0]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1]);
		grs422_frame.data_stream.accelY = ((uint16_t)(hCAN0.CanRxBuf.rx_data[2]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3]);
		grs422_frame.data_stream.accelZ = ((uint16_t)(hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]);
	}
	else if((CAN_ID_INS_GYRO == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;
		grs422_frameD.gyroX = ((uint16_t)(hCAN0.CanRxBuf.rx_data[0]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1]) * 0.0091552734375 - 300.0;
		grs422_frameD.gyroY = ((uint16_t)(hCAN0.CanRxBuf.rx_data[2]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3]) * 0.0091552734375 - 300.0;
		grs422_frameD.gyroZ = ((uint16_t)(hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]) * 0.0091552734375 - 300.0;
		grs422_frame.data_stream.gyroX = ((uint16_t)(hCAN0.CanRxBuf.rx_data[0]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1]);
		grs422_frame.data_stream.gyroY = ((uint16_t)(hCAN0.CanRxBuf.rx_data[2]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3]);
		grs422_frame.data_stream.gyroZ = ((uint16_t)(hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]);
	}
	else if((CAN_ID_INS_ANGLE == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;
		grs422_frameD.roll = (((uint16_t)hCAN0.CanRxBuf.rx_data[0]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1]) * 0.010986 - 360.0;
		grs422_frameD.pitch = (((uint16_t)hCAN0.CanRxBuf.rx_data[2]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3]) * 0.010986 - 360.0;
		grs422_frameD.azimuth = (((uint16_t)hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]) * 0.010986 - 360.0;
		grs422_frame.data_stream.roll = (((uint16_t)hCAN0.CanRxBuf.rx_data[0]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1]);
		grs422_frame.data_stream.pitch = (((uint16_t)hCAN0.CanRxBuf.rx_data[2]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3]);
		grs422_frame.data_stream.azimuth = (((uint16_t)hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]);
	}
	else if((CAN_ID_INS_HEIGHT == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;
		grs422_frameD.alt = (((uint32_t)hCAN0.CanRxBuf.rx_data[0]<<24) + (uint32_t)(hCAN0.CanRxBuf.rx_data[1] << 16)\
			+ (uint32_t)(hCAN0.CanRxBuf.rx_data[2] << 8) + (uint32_t)(hCAN0.CanRxBuf.rx_data[3] << 0)) * 0.001 - 10000.0;
		grs422_frameD.gpsSec = (((uint32_t)hCAN0.CanRxBuf.rx_data[4]<<24) + (uint32_t)(hCAN0.CanRxBuf.rx_data[5] << 16)\
			+ (uint32_t)(hCAN0.CanRxBuf.rx_data[6] << 8) + (uint32_t)(hCAN0.CanRxBuf.rx_data[7] << 0));
		
		grs422_frame.data_stream.altitude = ((uint32_t)(hCAN0.CanRxBuf.rx_data[0]<<24) + (uint32_t)(hCAN0.CanRxBuf.rx_data[1] << 16)\
			+ (uint32_t)(hCAN0.CanRxBuf.rx_data[1] << 8) + (uint32_t)(hCAN0.CanRxBuf.rx_data[0] << 0));
		grs422_frame.data_stream.gps_time = ((uint32_t)(hCAN0.CanRxBuf.rx_data[4]<<24) + (uint32_t)(hCAN0.CanRxBuf.rx_data[5] << 16)\
			+ (uint32_t)(hCAN0.CanRxBuf.rx_data[6] << 8) + (uint32_t)(hCAN0.CanRxBuf.rx_data[7] << 0));
	}
	else if((CAN_ID_INS_POSITION == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;
		grs422_frameD.lat = (((uint32_t)hCAN0.CanRxBuf.rx_data[0]<<24) + (uint32_t)(hCAN0.CanRxBuf.rx_data[1] << 16)\
			+ (uint32_t)(hCAN0.CanRxBuf.rx_data[2] << 8) + (uint32_t)(hCAN0.CanRxBuf.rx_data[3] << 0)) * 1e-7 -180.0;
		grs422_frameD.lon = (((uint32_t)hCAN0.CanRxBuf.rx_data[4]<<24) + (uint32_t)(hCAN0.CanRxBuf.rx_data[5] << 16)\
			+ (uint32_t)(hCAN0.CanRxBuf.rx_data[6] << 8) + (uint32_t)(hCAN0.CanRxBuf.rx_data[7] << 0)) * 1e-7 - 180.0;

		grs422_frame.data_stream.latitude = ((uint32_t)(hCAN0.CanRxBuf.rx_data[0]<<24) + (uint32_t)(hCAN0.CanRxBuf.rx_data[1] << 16)\
			+ (uint32_t)(hCAN0.CanRxBuf.rx_data[1] << 8) + (uint32_t)(hCAN0.CanRxBuf.rx_data[0] << 0));
		grs422_frame.data_stream.longitude = ((uint32_t)(hCAN0.CanRxBuf.rx_data[4]<<24) + (uint32_t)(hCAN0.CanRxBuf.rx_data[5] << 16)\
			+ (uint32_t)(hCAN0.CanRxBuf.rx_data[6] << 8) + (uint32_t)(hCAN0.CanRxBuf.rx_data[7] << 0));
	}
	else if((CAN_ID_INS_SPEED == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;
		grs422_frameD.vn = ((uint16_t)(hCAN0.CanRxBuf.rx_data[0]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1]) * 0.0030517 - 100.0;
		grs422_frameD.ve = ((uint16_t)(hCAN0.CanRxBuf.rx_data[2]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3]) * 0.0030517 - 100.0;
		grs422_frameD.vu = ((uint16_t)(hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]) * 0.0030517 - 100.0;
		grs422_frame.data_stream.vn = ((uint16_t)(hCAN0.CanRxBuf.rx_data[0]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1]);
		grs422_frame.data_stream.ve = ((uint16_t)(hCAN0.CanRxBuf.rx_data[2]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3]);
		grs422_frame.data_stream.vu = ((uint16_t)(hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]);
	}
	else if((CAN_ID_INS_STATUS == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;
		grs422_frameD.ins_gnssflag_pos = hCAN0.CanRxBuf.rx_data[0];
		grs422_frameD.ins_numsv = hCAN0.CanRxBuf.rx_data[1];
		grs422_frameD.ins_gnssflag_heading = hCAN0.CanRxBuf.rx_data[2];
		grs422_frameD.ins_self_check = hCAN0.CanRxBuf.rx_data[3];
		
		grs422_frameD.ins_car_status = hCAN0.CanRxBuf.rx_data[4];
		grs422_frameD.ins_gnss_week = ((uint16_t)(hCAN0.CanRxBuf.rx_data[5]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[6]);
		grs422_frameD.ins_status = hCAN0.CanRxBuf.rx_data[7];		
	}
	else if((CAN_ID_INS_STD_HEADING == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
	}
	else if((CAN_ID_INS_TEMPERATURE == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;
		grs422_frameD.sensor_temp = ((uint16_t)(hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]) * 0.01;
		
		float devicetemperature = ((uint16_t)(hCAN0.CanRxBuf.rx_data[4]<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[5]) * 0.01;
		devicetemperature += 0.00001;
	}
	#endif
	
#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD
		
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J6V
		can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);

	if((CAN_ID_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//轮速
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Back_Right = (float)((double)((((uint16_t)hCAN0.CanRxBuf.rx_data[7])<<8) + hCAN0.CanRxBuf.rx_data[6])*0.00390625);
		hINSData.CAN_Data.WheelSpeed_Back_Left = (float)((double)((((uint16_t)hCAN0.CanRxBuf.rx_data[5])<<8) + hCAN0.CanRxBuf.rx_data[4])*0.00390625);
		hINSData.CAN_Data.WheelSpeed_Front_Right = (float)((double)((((uint16_t)hCAN0.CanRxBuf.rx_data[3])<<8) + hCAN0.CanRxBuf.rx_data[2])*0.00390625);
		hINSData.CAN_Data.WheelSpeed_Front_Left = (float)((double)((((uint16_t)hCAN0.CanRxBuf.rx_data[1])<<8) + hCAN0.CanRxBuf.rx_data[0] )*0.00390625);
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if(g_front_detected == 1)
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}

	else if((CAN_ID_OTA == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}
#endif // VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J6V

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_AutoBots_RoboMix


	can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);

	if((CAN_ID_STATE == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//状态
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<8) + hCAN0.CanRxBuf.rx_data[0] )*0.1f;
		hINSData.CAN_Data.WheelSpeed_Back_Right = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.WheelSpeed_Back_Left = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.WheelSpeed_Front_Right = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.Gear = hCAN0.CanRxBuf.rx_data[7];
		hINSData.CAN_Data.WheelSteer = (((uint16_t)hCAN0.CanRxBuf.rx_data[5]<<8) + hCAN0.CanRxBuf.rx_data[4] )*0.1f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if(g_front_detected == 1)
		{
			g_can0_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}

	else if((CAN_ID_OTA == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_AutoBots_RoboMix

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BoLeiDun

	can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);

	if((CAN_ID_STATE == hCAN0.CanRxBuf.rx_efid)&&(CAN_FF_EXTENDED == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//状态
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)hCAN0.CanRxBuf.rx_data[5]<<8) + hCAN0.CanRxBuf.rx_data[4] )*1.0f/256.0f;
		hINSData.CAN_Data.WheelSpeed_Back_Right = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.WheelSpeed_Back_Left = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.WheelSpeed_Front_Right = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.Gear = hCAN0.CanRxBuf.rx_data[1] & 0x0F;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_front_detected == 1)&&(g_gear_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_STEER == hCAN0.CanRxBuf.rx_efid)&&(CAN_FF_EXTENDED == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//方向盘
		g_gear_detected = 1;
		hINSData.CAN_Data.WheelSteer = (((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<8) + hCAN0.CanRxBuf.rx_data[0] - 1575 )*0.1f ;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_front_detected == 1)&&(g_gear_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_OTA == hCAN0.CanRxBuf.rx_efid)&&(CAN_FF_EXTENDED == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_BoLeiDun

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang
	can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);
	if((CAN_ID_GEAR == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft)){
		hCAN0.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = (hCAN0.CanRxBuf.rx_data[7] & 0xE0)>>5;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1))
		{
			g_can0_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//前轮轮速
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Left = ((uint16_t)((hCAN0.CanRxBuf.rx_data[1]&0x7F) +(((uint16_t)hCAN0.CanRxBuf.rx_data[0])<<7)))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Front_Right = ((uint16_t)((hCAN0.CanRxBuf.rx_data[3]&0x7F) +(((uint16_t)hCAN0.CanRxBuf.rx_data[2])<<7)))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Back_Left = ((uint16_t)((hCAN0.CanRxBuf.rx_data[5]&0x7F) +(((uint16_t)hCAN0.CanRxBuf.rx_data[4])<<7)))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Back_Right = ((uint16_t)((hCAN0.CanRxBuf.rx_data[7]&0x7F) +(((uint16_t)hCAN0.CanRxBuf.rx_data[6])<<7)))*0.01f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1))
		{
			g_can0_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
//	else if((CAN_ID_ABS == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
//		hCAN0.RecvdFlag = SET;		//ABS轮速
//		g_back_detected = 1;
//		hINSData.CAN_Data.WheelSpeed_Back_Right = (((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN0.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN0.CanRxBuf.rx_data[2]>>7))*0.01f;
//		hINSData.CAN_Data.WheelSpeed_Back_Left = (((uint16_t)(hCAN0.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN0.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN0.CanRxBuf.rx_data[4]>>6))*0.01f;
//		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
//		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
//		{
//			g_gear_detected = 0;
//			g_front_detected = 0;
//			g_back_detected = 0;
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_High();
//		}
//	}
	else if((CAN_ID_OTA == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}
#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

	can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);
	if((CAN_ID_GEAR == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft)){
		hCAN0.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = hCAN0.CanRxBuf.rx_data[3] & 0x0F;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_can0_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WHEEL_FRONT == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//前轮轮速
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Left = ((uint16_t)(hCAN0.CanRxBuf.rx_data[1] +(((uint16_t)hCAN0.CanRxBuf.rx_data[0]&0x3F)<<8)))*0.00868f;
		hINSData.CAN_Data.WheelSpeed_Front_Right = ((uint16_t)(hCAN0.CanRxBuf.rx_data[3] +(((uint16_t)hCAN0.CanRxBuf.rx_data[2]&0x3F)<<8)))*0.00868f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_can0_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WHEEL_BACK == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//后轮轮速
		g_back_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Back_Left = ((uint16_t)(hCAN0.CanRxBuf.rx_data[1] +(((uint16_t)hCAN0.CanRxBuf.rx_data[0]&0x3F)<<8)))*0.00868f;
		hINSData.CAN_Data.WheelSpeed_Back_Right = ((uint16_t)(hCAN0.CanRxBuf.rx_data[3] +(((uint16_t)hCAN0.CanRxBuf.rx_data[2]&0x3F)<<8)))*0.00868f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_can0_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_OTA == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70

can_message_receive(hCAN0.canDev, CAN_FIFO0, &hCAN0.CanRxBuf);
	if((CAN_ID_GEAR == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft)){
		hCAN0.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = (hCAN0.CanRxBuf.rx_data[1] & 0x1C)>>2;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1))
		{
			g_can0_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
			g_CAN_Count ++;
		}
	}
	else if((CAN_ID_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//轮速
		g_front_detected = 1;
		if((hCAN0.CanRxBuf.rx_data[0]&0x80) == 0)
			hINSData.CAN_Data.WheelSpeed_Front_Left = ((uint16_t)((((uint16_t)hCAN0.CanRxBuf.rx_data[1])<<7) +(hCAN0.CanRxBuf.rx_data[0]&0x7F)))*0.01f;
		if((hCAN0.CanRxBuf.rx_data[2]&0x80) == 0)
			hINSData.CAN_Data.WheelSpeed_Front_Right = ((uint16_t)((((uint16_t)hCAN0.CanRxBuf.rx_data[3])<<7) +(hCAN0.CanRxBuf.rx_data[2]&0x7F)))*0.01f;
		if((hCAN0.CanRxBuf.rx_data[4]&0x80) == 0)
			hINSData.CAN_Data.WheelSpeed_Back_Left = ((uint16_t)((((uint16_t)hCAN0.CanRxBuf.rx_data[5])<<7) +(hCAN0.CanRxBuf.rx_data[4]&0x7F)))*0.01f;
		if((hCAN0.CanRxBuf.rx_data[6]&0x80) == 0)
			hINSData.CAN_Data.WheelSpeed_Back_Right = ((uint16_t)((((uint16_t)hCAN0.CanRxBuf.rx_data[7])<<7) +(hCAN0.CanRxBuf.rx_data[6]&0x7F)))*0.01f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1))
		{
			g_can0_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
			g_CAN_Count ++;
		}
	}
	else if((CAN_ID_OTA == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70

}

/*!
    \brief      this function handles CAN1 RX0 exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void CAN1_RX0_IRQHandler(void)
{
	
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD
	can_message_receive(hCAN1.canDev, CAN_FIFO0, &hCAN1.CanRxBuf);
	//gcan0_rx_syn = 1;
	//memcpy(&gCanRxBuf, &hCAN0.CanRxBuf, sizeof(gCanRxBuf));
	if((CAN_ID_INS_ACC == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft)){
		hCAN1.RecvdFlag = SET;		//档位
	}
//	else if((CAN_ID_FRONT_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
//		hCAN0.RecvdFlag = SET;		//前轮轮速
//		g_front_detected = 1;
//		hINSData.CAN_Data.WheelSpeed_Front_Right = ((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x3F)<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1])*0.008868f * 3.6;
//		hINSData.CAN_Data.WheelSpeed_Front_Left =  ((uint16_t)((hCAN0.CanRxBuf.rx_data[2]&0x3F)<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3])*0.008868f * 3.6;
//		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
//		combineData.canInfo.data.WheelSpeed_Front_Left = hINSData.CAN_Data.WheelSpeed_Front_Left; 
//		combineData.canInfo.data.WheelSpeed_Front_Right = hINSData.CAN_Data.WheelSpeed_Front_Right; 
//		combineData.canInfo.counter++;			
//		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
//		{
//			g_gear_detected = 0;
//			g_front_detected = 0;
//			g_back_detected = 0;
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_High();
//		}
//	}
//	else if((CAN_ID_BACK_WHEEL == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
//		hCAN0.RecvdFlag = SET;		//后轮轮速
//		g_back_detected = 1;
//		hINSData.CAN_Data.WheelSpeed_Back_Right = ((uint16_t)((hCAN0.CanRxBuf.rx_data[0]&0x3F)<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[1])*0.008868f * 3.6;
//		hINSData.CAN_Data.WheelSpeed_Back_Left =  ((uint16_t)((hCAN0.CanRxBuf.rx_data[2]&0x3F)<<8) +(uint16_t)hCAN0.CanRxBuf.rx_data[3])*0.008868f * 3.6;
//		//DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CcanDataTypeDef));
//		combineData.canInfo.data.WheelSpeed_Back_Left = hINSData.CAN_Data.WheelSpeed_Back_Left; 
//		combineData.canInfo.data.WheelSpeed_Back_Right = hINSData.CAN_Data.WheelSpeed_Back_Right; 
//		combineData.canInfo.counter++;			
//		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
//		{
//			g_gear_detected = 0;
//			g_front_detected = 0;
//			g_back_detected = 0;
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_High();
//		}
//	}
//	else if((CAN_ID_WHEEL_STEER == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
//		hCAN0.RecvdFlag = SET;		//方向盘
//		hINSData.CAN_Data.WheelSteer = 0;//hCAN0.CanRxBuf.rx_data[0]*180.0f/65536.0f;
//		combineData.canInfo.data.WheelSteer = hINSData.CAN_Data.WheelSteer; 
//		combineData.canInfo.counter++;			
//	}
//	else if((CAN_ID_ODOMETER == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
//		hCAN0.RecvdFlag = SET;		//里程计
//		hINSData.CAN_Data.OdoPulse_1 = 0;//hCAN0.CanRxBuf.rx_data[0]*0.00863f;
//		hINSData.CAN_Data.OdoPulse_2 = 0;//hCAN0.CanRxBuf.rx_data[0]*0.00863f;
//		combineData.canInfo.data.OdoPulse_1 = hINSData.CAN_Data.OdoPulse_1; 
//		combineData.canInfo.data.OdoPulse_2 = hINSData.CAN_Data.OdoPulse_2; 
//		combineData.canInfo.counter++;	
//	}
//	else if((CAN_ID_OTA == hCAN0.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN0.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN0.CanRxBuf.rx_ft) ){
//		hCAN0.RecvdFlag = SET;		//OTA
//		NVIC_SystemReset();
//	}
#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_BYD
	
	/* check the receive message */
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV
	can_message_receive(hCAN1.canDev, CAN_FIFO0, &hCAN1.CanRxBuf);
	if((CAN_ID_GEAR == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft)){
		hCAN1.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = hCAN1.CanRxBuf.rx_data[3] & 0x07;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_FRONT_WHEEL == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//前轮轮速
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Right = (((uint16_t)((hCAN1.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN1.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN1.CanRxBuf.rx_data[2]>>7))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)(hCAN1.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN1.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN1.CanRxBuf.rx_data[4]>>6))*0.01f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_BACK_WHEEL == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//后轮轮速
		g_back_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Back_Right = (((uint16_t)((hCAN1.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN1.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN1.CanRxBuf.rx_data[2]>>7))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Back_Left = (((uint16_t)(hCAN1.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN1.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN1.CanRxBuf.rx_data[4]>>6))*0.01f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WHEEL_STEER == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//方向盘
		hINSData.CAN_Data.WheelSteer = 0;//hCAN1.CanRxBuf.rx_data[0]*180.0f/65536.0f;
	}
	else if((CAN_ID_ODOMETER == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//里程计
		hINSData.CAN_Data.OdoPulse_1 = 0;//hCAN1.CanRxBuf.rx_data[0]*0.00863f;
		hINSData.CAN_Data.OdoPulse_2 = 0;//hCAN1.CanRxBuf.rx_data[0]*0.00863f;
	}
	else if((CAN_ID_OTA == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}
#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_WuLing_MiniEV
		
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J6V
	can_message_receive(hCAN1.canDev, CAN_FIFO0, &hCAN1.CanRxBuf);

	if((CAN_ID_WHEEL == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//轮速
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Back_Right = (((uint16_t)hCAN1.CanRxBuf.rx_data[7]<<8) + hCAN1.CanRxBuf.rx_data[6])*0.00390625f;
		hINSData.CAN_Data.WheelSpeed_Back_Left = (((uint16_t)hCAN1.CanRxBuf.rx_data[5]<<8) + hCAN1.CanRxBuf.rx_data[4])*0.00390625f;
		hINSData.CAN_Data.WheelSpeed_Front_Right = (((uint16_t)hCAN1.CanRxBuf.rx_data[3]<<8) + hCAN1.CanRxBuf.rx_data[2])*0.00390625f;
		hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)hCAN1.CanRxBuf.rx_data[1]<<8) + hCAN1.CanRxBuf.rx_data[0] )*0.00390625f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if(g_front_detected == 1)
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}

	else if((CAN_ID_OTA == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}
#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J6V

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_AutoBots_RoboMix

	can_message_receive(hCAN1.canDev, CAN_FIFO0, &hCAN1.CanRxBuf); 
	if((CAN_ID_STATE == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//状态
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)hCAN1.CanRxBuf.rx_data[1]<<8) + hCAN1.CanRxBuf.rx_data[0] )*0.1f;
		hINSData.CAN_Data.WheelSpeed_Back_Right = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.WheelSpeed_Back_Left = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.WheelSpeed_Front_Right = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.Gear = hCAN1.CanRxBuf.rx_data[7];
		hINSData.CAN_Data.WheelSteer = (((uint16_t)hCAN1.CanRxBuf.rx_data[5]<<8) + hCAN1.CanRxBuf.rx_data[4] )*0.1f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if(g_front_detected == 1)
		{
			g_can1_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_OTA == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_AutoBots_RoboMix

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_BoLeiDun

	can_message_receive(hCAN1.canDev, CAN_FIFO0, &hCAN1.CanRxBuf); 
	if((CAN_ID_STATE == hCAN1.CanRxBuf.rx_efid)&&(CAN_FF_EXTENDED == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//状态
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Left = (((uint16_t)hCAN1.CanRxBuf.rx_data[5]<<8) + hCAN1.CanRxBuf.rx_data[4] )*1.0f/256.0f;
		hINSData.CAN_Data.WheelSpeed_Back_Right = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.WheelSpeed_Back_Left = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.WheelSpeed_Front_Right = hINSData.CAN_Data.WheelSpeed_Front_Left;
		hINSData.CAN_Data.Gear = hCAN1.CanRxBuf.rx_data[1] & 0x0F;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_front_detected == 1)&&(g_gear_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_STEER == hCAN1.CanRxBuf.rx_efid)&&(CAN_FF_EXTENDED == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//方向盘
		g_gear_detected = 1;
		hINSData.CAN_Data.WheelSteer = (((uint16_t)hCAN1.CanRxBuf.rx_data[1]<<8) + hCAN1.CanRxBuf.rx_data[0] - 1575)*0.1f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_front_detected == 1)&&(g_gear_detected == 1))
		{
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_OTA == hCAN1.CanRxBuf.rx_efid)&&(CAN_FF_EXTENDED == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_BoLeiDun

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang
	can_message_receive(hCAN1.canDev, CAN_FIFO0, &hCAN1.CanRxBuf);
	if((CAN_ID_GEAR == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft)){
		hCAN1.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = (hCAN1.CanRxBuf.rx_data[7] & 0xE0)>>5;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1))
		{
			g_can1_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WHEEL == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//轮速
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Left = ((uint16_t)((hCAN1.CanRxBuf.rx_data[1]&0x7F) +(((uint16_t)hCAN1.CanRxBuf.rx_data[0])<<7)))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Front_Right = ((uint16_t)((hCAN1.CanRxBuf.rx_data[3]&0x7F) +(((uint16_t)hCAN1.CanRxBuf.rx_data[2])<<7)))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Back_Left = ((uint16_t)((hCAN1.CanRxBuf.rx_data[5]&0x7F) +(((uint16_t)hCAN1.CanRxBuf.rx_data[4])<<7)))*0.01f;
		hINSData.CAN_Data.WheelSpeed_Back_Right = ((uint16_t)((hCAN1.CanRxBuf.rx_data[7]&0x7F) +(((uint16_t)hCAN1.CanRxBuf.rx_data[6])<<7)))*0.01f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1))
		{
			g_can1_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
//	else if((CAN_ID_ABS == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
//		hCAN1.RecvdFlag = SET;		//ABS轮速
//		g_back_detected = 1;
//		hINSData.CAN_Data.WheelSpeed_Back_Right = (((uint16_t)((hCAN1.CanRxBuf.rx_data[0]&0x7F)<<9)) +((uint16_t)hCAN1.CanRxBuf.rx_data[1]<<1)+((uint16_t)hCAN1.CanRxBuf.rx_data[2]>>7))*0.01f;
//		hINSData.CAN_Data.WheelSpeed_Back_Left = (((uint16_t)(hCAN1.CanRxBuf.rx_data[2]&0x3F)<<10) +((uint16_t)hCAN1.CanRxBuf.rx_data[3]<<2) + ((uint16_t)hCAN1.CanRxBuf.rx_data[4]>>6))*0.01f;
//		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
//		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
//		{
//			g_gear_detected = 0;
//			g_front_detected = 0;
//			g_back_detected = 0;
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_High();
//		}
//	}
	else if((CAN_ID_OTA == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}
#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFeng_YueXiang
	
#if VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

	can_message_receive(hCAN1.canDev, CAN_FIFO0, &hCAN1.CanRxBuf);
	if((CAN_ID_GEAR == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft)){
		hCAN1.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = hCAN1.CanRxBuf.rx_data[3] & 0x0F;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_can1_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WHEEL_FRONT == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//前轮轮速
		g_front_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Front_Left = ((uint16_t)(hCAN1.CanRxBuf.rx_data[1] +(((uint16_t)hCAN1.CanRxBuf.rx_data[0]&0x3F)<<8)))*0.00868f;
		hINSData.CAN_Data.WheelSpeed_Front_Right = ((uint16_t)(hCAN1.CanRxBuf.rx_data[3] +(((uint16_t)hCAN1.CanRxBuf.rx_data[2]&0x3F)<<8)))*0.00868f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_can1_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_WHEEL_BACK == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//后轮轮速
		g_back_detected = 1;
		hINSData.CAN_Data.WheelSpeed_Back_Left = ((uint16_t)(hCAN1.CanRxBuf.rx_data[1] +(((uint16_t)hCAN1.CanRxBuf.rx_data[0]&0x3F)<<8)))*0.00868f;
		hINSData.CAN_Data.WheelSpeed_Back_Right = ((uint16_t)(hCAN1.CanRxBuf.rx_data[3] +(((uint16_t)hCAN1.CanRxBuf.rx_data[2]&0x3F)<<8)))*0.00868f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1)&&(g_back_detected == 1))
		{
			g_can1_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
		}
	}
	else if((CAN_ID_OTA == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}

#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_YiQiJieFang_J7

#if VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70
can_message_receive(hCAN1.canDev, CAN_FIFO0, &hCAN1.CanRxBuf);
	if((CAN_ID_GEAR == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft)){
		hCAN1.RecvdFlag = SET;		//档位
		g_gear_detected = 1;
		hINSData.CAN_Data.Gear = (hCAN1.CanRxBuf.rx_data[1] & 0x1C)>>2;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1))
		{
			g_can1_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
			g_CAN_Count ++;
		}
	}
	else if((CAN_ID_WHEEL == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN1.RecvdFlag = SET;		//前轮轮速
		g_front_detected = 1;
		if((hCAN1.CanRxBuf.rx_data[0]&0x80) == 0)
			hINSData.CAN_Data.WheelSpeed_Front_Left = ((uint16_t)((((uint16_t)hCAN1.CanRxBuf.rx_data[1])<<7) +(hCAN1.CanRxBuf.rx_data[0]&0x7F)))*0.01f;
		if((hCAN1.CanRxBuf.rx_data[2]&0x80) == 0)
			hINSData.CAN_Data.WheelSpeed_Front_Right = ((uint16_t)((((uint16_t)hCAN1.CanRxBuf.rx_data[3])<<7) +(hCAN1.CanRxBuf.rx_data[2]&0x7F)))*0.01f;
		if((hCAN1.CanRxBuf.rx_data[4]&0x80) == 0)
			hINSData.CAN_Data.WheelSpeed_Back_Left = ((uint16_t)((((uint16_t)hCAN1.CanRxBuf.rx_data[5])<<7) +(hCAN1.CanRxBuf.rx_data[4]&0x7F)))*0.01f;
		if((hCAN1.CanRxBuf.rx_data[6]&0x80) == 0)
			hINSData.CAN_Data.WheelSpeed_Back_Right = ((uint16_t)((((uint16_t)hCAN1.CanRxBuf.rx_data[7])<<7) +(hCAN1.CanRxBuf.rx_data[6]&0x7F)))*0.01f;
		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
		if((g_gear_detected == 1)&&(g_front_detected == 1))
		{
			g_can1_rx_flag = 1;
			g_gear_detected = 0;
			g_front_detected = 0;
			g_back_detected = 0;
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_Low();
			ARM2_OUTPUT_ARM1_High();
			g_CAN_Count ++;
		}
	}
	else if((CAN_ID_OTA == hCAN1.CanRxBuf.rx_sfid)&&(CAN_FF_STANDARD == hCAN1.CanRxBuf.rx_ff)&&(CAN_FT_DATA == hCAN1.CanRxBuf.rx_ft) ){
		hCAN0.RecvdFlag = SET;		//OTA
		NVIC_SystemReset();
	}
#endif //VEHICLE_TYPE_USED == VEHICLE_TYPE_DongFengFengShen_E70

}


