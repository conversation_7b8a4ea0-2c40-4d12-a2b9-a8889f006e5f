/***********************************************************************************
nav status and output module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"
#include "nav.h"

unsigned char g_NavStatusChangeFlag 			= RETURN_FAIL;
unsigned char g_NavStandardFlagChangeFlag 		= RETURN_FAIL;
unsigned char g_NavFunsionSourceChangeFlag 		= RETURN_FAIL;
unsigned char g_NavRtkStatusChangeFlag			= RETURN_FAIL;

char g_OBSIMUBuff[1024]={0};
char g_OBSGNSSBuff[1024]={0};
char g_SINSBuff[1024]={0};


//发送IMU观测量数据
void SendOBSIMUData(_NAV_Data_Out_t* NAV_Data_Out_temp,_NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned short len=0;
	char checksum=0;
	char tmp[8]={0};
	memset(g_OBSIMUBuff,0,sizeof(g_OBSIMUBuff));
	sprintf(g_OBSIMUBuff, "$OBSIMU,%d,%d,%d,%.7f,%.7f,%.7f,%d,%.7f,%.7f,%.7f,%d,%.7f,%.7f,%.7f", \
            NAV_Data_Full_p->GPS.gpsweek,NAV_Data_Full_p->GPS.gpssecond,1,NAV_Data_Out_temp->gyroX,NAV_Data_Out_temp->gyroY,NAV_Data_Out_temp->gyroZ,\
            1,NAV_Data_Out_temp->accelX,NAV_Data_Out_temp->accelY,NAV_Data_Out_temp->accelZ,
            1,NAV_Data_Out_temp->magnetX,NAV_Data_Out_temp->magnetY,NAV_Data_Out_temp->magnetZ);
	len = (unsigned short)strlen(g_OBSIMUBuff);
	checksum=CalculateCheckSum(g_OBSIMUBuff,(unsigned short)len);
	
	strcat(g_OBSIMUBuff,"*");
	
	memset(tmp,0,sizeof(tmp));
	sprintf(tmp,"%02X\r\n",checksum);
	strcat(g_OBSIMUBuff,tmp);

	len = (unsigned short)strlen(g_OBSIMUBuff);
	
#ifdef linux	
	printf(g_OBSIMUBuff);
#else
	Arm_SendMsg(g_OBSIMUBuff,len);
#endif

}
//发送GNSS观测量数据
void SendOBSGNSSData(_NAV_Data_Out_t* NAV_Data_Out_temp,_NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned short len=0;
	char checksum=0;
	char tmp[8]={0};
	char str_LON_Hemisphere=E_LON_EAST_Hemisphere;
	char str_LAT_Hemisphere=E_LAT_NORTH_Hemisphere;
	memset(g_OBSGNSSBuff,0,sizeof(g_OBSGNSSBuff));

	if(NAV_Data_Full_p->GPS.Lat<0)
	{
		str_LAT_Hemisphere = E_LAT_SOUTH_Hemisphere;
	}
	if(NAV_Data_Full_p->GPS.Lon<0)
	{
		str_LON_Hemisphere = E_LON_WEST_Hemisphere;
	}
	
	
	sprintf(g_OBSGNSSBuff, "$OBSGNSS,%d,%d,%d,%.7f,%c,%.7f,%c,%.7f,%d,%.7f,%d,%.7f,%.7f,%.7f", \
            NAV_Data_Full_p->GPS.gpsweek,NAV_Data_Full_p->GPS.gpssecond,\
            NAV_Data_Full_p->GPS.Position_Status,fabs(NAV_Data_Full_p->GPS.Lat),str_LAT_Hemisphere,\
            fabs(NAV_Data_Full_p->GPS.Lon),str_LON_Hemisphere,NAV_Data_Full_p->GPS.Altitude,\
            NAV_Data_Full_p->GPS.rtkStatus,NAV_Data_Full_p->GPS.Heading,\
            2,NAV_Data_Full_p->GPS.ve,NAV_Data_Full_p->GPS.vn,NAV_Data_Full_p->GPS.vu);

	len = (unsigned short)strlen(g_OBSGNSSBuff);
	checksum=CalculateCheckSum(g_OBSGNSSBuff,len);
	
	strcat(g_OBSGNSSBuff,"*");
	
	memset(tmp,0,sizeof(tmp));
	sprintf(tmp,"%02X\r\n",checksum);
	strcat(g_OBSGNSSBuff,tmp);

	len = (unsigned short)strlen(g_OBSGNSSBuff);
	
#ifdef linux	
	printf(g_OBSGNSSBuff);
#else
	Arm_SendMsg(g_OBSGNSSBuff,len);
#endif
	

}
//发送组合导航数据
void SendSINSData(_NAV_Data_Out_t* NAV_Data_Out_temp,_NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned short len=0;
	char checksum=0;
	char tmp[8]={0};
	char str_LON_Hemisphere=E_LON_EAST_Hemisphere;
	char str_LAT_Hemisphere=E_LAT_NORTH_Hemisphere;

	if(NAV_Data_Out_temp->latitude<0)
	{
		str_LAT_Hemisphere = E_LAT_SOUTH_Hemisphere;
	}
	if(NAV_Data_Out_temp->longitude<0)
	{
		str_LON_Hemisphere = E_LON_WEST_Hemisphere;
	}
	
	memset(g_SINSBuff,0,sizeof(g_SINSBuff));
	sprintf(g_SINSBuff,"$OBSGNSS,%d,%d,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%.7f,%c,%.7f,%c,%.7f", \
		 NAV_Data_Full_p->GPS.gpsweek,NAV_Data_Full_p->GPS.gpssecond,\
		 NAV_Data_Out_temp->pitch,NAV_Data_Out_temp->roll,NAV_Data_Out_temp->heading,\
		 NAV_Data_Out_temp->ve,NAV_Data_Out_temp->vn,NAV_Data_Out_temp->vu,\
		 fabs(NAV_Data_Out_temp->latitude),str_LAT_Hemisphere,
		 fabs(NAV_Data_Out_temp->longitude),str_LON_Hemisphere,NAV_Data_Out_temp->altitude);

	len = (unsigned short)strlen(g_SINSBuff);
	checksum=CalculateCheckSum(g_SINSBuff,len);
	
	strcat(g_SINSBuff,"*");
	
	memset(tmp,0,sizeof(tmp));
	sprintf(tmp,"%02X\r\n",checksum);
	strcat(g_SINSBuff,tmp);

	len = (unsigned short)strlen(g_SINSBuff);
	
#ifdef linux	
	printf(g_SINSBuff);
#else
	Arm_SendMsg(g_SINSBuff,len);
#endif
	

}


/******************************************************************************
*原  型：void Out_Data_Up(_NAV_Data_Out_t* NAV_Data_Out_temp)
*功  能：导航解算结果输出
*输  入：无
*输  出：无
*******************************************************************************/
void Out_Data_Up(_NAV_Data_Out_t* NAV_Data_Out_temp)
{
	NAV_Data_Out_temp->imuSelect    =	(unsigned char)NAV_Data_Full.imuSelect;
	NAV_Data_Out_temp->memsType		=	(unsigned char)NAV_Data_Full.memsType;
	NAV_Data_Out_temp->use_gps_flag	=	NAV_Data_Full.KF.use_gps_flag;
	NAV_Data_Out_temp->fusion_source	=	NAV_Data_Full.KF.fusion_source;
	//IMU
//	NAV_Data_Out_temp->gyroX = NAV_Data_Full.IMU.gyro_use[0]*RAD2DEG;
//	NAV_Data_Out_temp->gyroY = NAV_Data_Full.IMU.gyro_use[1]*RAD2DEG;
//	NAV_Data_Out_temp->gyroZ = NAV_Data_Full.IMU.gyro_use[2]*RAD2DEG;
//	
//	NAV_Data_Out_temp->accelX = NAV_Data_Full.IMU.acc_use[0];
//	NAV_Data_Out_temp->accelY = NAV_Data_Full.IMU.acc_use[1];
//	NAV_Data_Out_temp->accelZ = NAV_Data_Full.IMU.acc_use[2];
	//20250102修改，输出默认轴向数据
	NAV_Data_Out_temp->gyroX = NAV_Data_Full.IMU.gyro[0];
	NAV_Data_Out_temp->gyroY = NAV_Data_Full.IMU.gyro[1];
	NAV_Data_Out_temp->gyroZ = NAV_Data_Full.IMU.gyro[2];
	
	NAV_Data_Out_temp->accelX = NAV_Data_Full.IMU.acc[0]/g0;
	NAV_Data_Out_temp->accelY = NAV_Data_Full.IMU.acc[1]/g0;
	NAV_Data_Out_temp->accelZ = NAV_Data_Full.IMU.acc[2]/g0;
	
	NAV_Data_Out_temp->magnetX = NAV_Data_Full.MAGNET.mag_use[0];
	NAV_Data_Out_temp->magnetY = NAV_Data_Full.MAGNET.mag_use[1];
	NAV_Data_Out_temp->magnetZ = NAV_Data_Full.MAGNET.mag_use[2];
	//fusion

	
	double MpvCnb[3* 3],MpvCnblever[3],Cwlever[3];
	double CW_[3*3],CW[3*3];
	matmul("NN", 3, 3, 3, 1.0, NAV_Data_Full.SINS.Mpv, NAV_Data_Full.SINS.Cb2n, 0.0, MpvCnb);
    matmul("NN", 3, 1, 3, 1.0, MpvCnb, NAV_Data_Full.Param.gnssArmLength, 0.0, MpvCnblever);


	askew(NAV_Data_Full.SINS.wb_ib, CW_);  //[wnb X]//反对称矩阵形式

	matmul("NN", 3, 3, 3, 1.0, NAV_Data_Full.SINS.Cb2n, CW_, 0.0, CW);
	//matmul("NN", 3, 1, 3, 1.0, m_sins.Mpv, m_sins.vn, 0.0, MpvVn);
	matmul("NN", 3, 1, 3, 1.0, CW, NAV_Data_Full.Param.gnssArmLength, 0.0, Cwlever);  //wnb x lever

	//********dv,dpos*******
	double pos[3];
	{
		// 反推IMU状态（关键：避免累积误差）
       pos[0] = (NAV_Data_Full.SINS.pos[0] + MpvCnblever[0])*RAD2DEG;
		pos[1] = (NAV_Data_Full.SINS.pos[1] + MpvCnblever[1])*RAD2DEG;

		pos[2]= NAV_Data_Full.SINS.pos[2] + MpvCnblever[2];

		// NAV_Data_Full_p->SINS.vn_antenna[i]  = NAV_Data_Full.SINS.vn[i] + Cwlever[i];
	}
	NAV_Data_Out_temp->latitude = pos[0];
	NAV_Data_Out_temp->longitude =pos[1];
	NAV_Data_Out_temp->altitude = pos[2];

	NAV_Data_Out_temp->ve = NAV_Data_Full.SINS.vn[0];
	NAV_Data_Out_temp->vn = NAV_Data_Full.SINS.vn[1];
	NAV_Data_Out_temp->vu = NAV_Data_Full.SINS.vn[2];
	//NAV_Data_Out_temp->vu = NAV_Data_Full.GPS.baseline;//**改动****
	
//	NAV_Data_Out_temp->pitch = NAV_Data_Full.SINS.att[0]*RAD2DEG - NAV_Data_Full.ODS.att_ods2_b_filte[0];//*************
//	NAV_Data_Out_temp->roll = NAV_Data_Full.SINS.att[1]*RAD2DEG;
//	NAV_Data_Out_temp->heading = CorrHeading(NAV_Data_Full.SINS.att[2]*RAD2DEG - NAV_Data_Full.ODS.att_ods2_b_filte[2]);
	NAV_Data_Out_temp->pitch   = NAV_Data_Full.att_body[0]*RAD2DEG;//*********
	NAV_Data_Out_temp->roll    = NAV_Data_Full.att_body[1]*RAD2DEG;
	NAV_Data_Out_temp->heading = NAV_Data_Full.att_body[2]*RAD2DEG;
	//if(!(NAV_Data_Full.ODS.ods_flag))//*****未接入轮速***
		if(1)
	{
		NAV_Data_Out_temp->roll = NAV_Data_Full.SINS.att[1]*RAD2DEG;
	}
	//************加载过系统参数，尚未进入组合状态***************->*********
	if((NAV_Data_Full.Nav_Status>0)
	&& (!(NAV_Data_Full.Pre_att_flag))
	  )
	{
		 	NAV_Data_Out_temp->pitch   = NAV_Data_Full.Pre_att[0]*RAD2DEG;//*********
	   NAV_Data_Out_temp->roll    = NAV_Data_Full.Pre_att[1]*RAD2DEG;
	   NAV_Data_Out_temp->heading = NAV_Data_Full.Pre_att[2]*RAD2DEG;
		 	NAV_Data_Out_temp->ve = 0.0;
	   NAV_Data_Out_temp->vn = 0.0;
	   NAV_Data_Out_temp->vu = 0.0;
		 	NAV_Data_Out_temp->latitude = 0.0;
	   NAV_Data_Out_temp->longitude = 0.0;
	   NAV_Data_Out_temp->altitude = 0.0;
				NAV_Data_Out_temp->rtkStatus =0;
		  NAV_Data_Out_temp->Sate_Num =0;
	}
	//************加载过系统参数，尚未进入组合状态***************<-*********
	//gps
//	NAV_Data_Out_temp->latitude_gps = NAV_Data_Full.GPS.Lat;
//	NAV_Data_Out_temp->longitude_gps = NAV_Data_Full.GPS.Lon;
//	NAV_Data_Out_temp->altitude_gps = NAV_Data_Full.GPS.Altitude;
//	
//	NAV_Data_Out_temp->ve_gps = NAV_Data_Full.GPS.ve;
//	NAV_Data_Out_temp->vn_gps = NAV_Data_Full.GPS.vn;
//	NAV_Data_Out_temp->vu_gps = NAV_Data_Full.GPS.vu;
	
	NAV_Data_Out_temp->pitch_gps = NAV_Data_Full.GPS.Pitch;
	NAV_Data_Out_temp->roll_gps = NAV_Data_Full.GPS.Roll;
	NAV_Data_Out_temp->heading_gps = NAV_Data_Full.GPS.Heading;
	
 NAV_Data_Out_temp->rtkStatus =NAV_Data_Full.SINS.Init_flag;// NAV_Data_Full.GPS.rtkStatus;	
	NAV_Data_Out_temp->Sate_Num = NAV_Data_Full.GPS.Sate_Num;
	NAV_Data_Out_temp->Position_Status = NAV_Data_Full.GPS.Position_Status;
	NAV_Data_Out_temp->gpssecond = NAV_Data_Full.GPS.gpssecond;
	NAV_Data_Out_temp->delay = NAV_Data_Full.GPS.delay_pps;//*****改动***
	NAV_Data_Out_temp->pdop = NAV_Data_Full.GPS.pdop;//*****改动***
	//ods
 
    //标定参数
	if(1)
	{
		NAV_Data_Out_temp->Nav_Standard_flag = NAV_Data_Full.Nav_Standard_flag;
		
		NAV_Data_Out_temp->gnssAtt_from_vehicle2[0] = NAV_Data_Full.Param.gnssAtt_from_vehicle2[0];
		NAV_Data_Out_temp->gnssAtt_from_vehicle2[1] = NAV_Data_Full.Param.gnssAtt_from_vehicle2[1];
		NAV_Data_Out_temp->gnssAtt_from_vehicle2[2] = NAV_Data_Full.Param.gnssAtt_from_vehicle2[2];		
		
		NAV_Data_Out_temp->gyro_off[0] = NAV_Data_Full.SINS.eb[0];
		NAV_Data_Out_temp->gyro_off[1] = NAV_Data_Full.SINS.eb[1];
		NAV_Data_Out_temp->gyro_off[2] = NAV_Data_Full.SINS.eb[2];
		NAV_Data_Out_temp->acc_off[0] = NAV_Data_Full.SINS.db[0];
		NAV_Data_Out_temp->acc_off[1] = NAV_Data_Full.SINS.db[1];
		NAV_Data_Out_temp->acc_off[2] = NAV_Data_Full.SINS.db[2];		
	}
    //sys******输出状态定义*****
	if(NAV_Data_Full.Nav_Status<E_NAV_STATUS_SYSTEM_STANDARD)
	{	
		NAV_Data_Out_temp->Nav_Status = 0; //导航准备
	}
	else if(NAV_Data_Full.Nav_Status == E_NAV_STATUS_SYSTEM_STANDARD)
	{
		NAV_Data_Out_temp->Nav_Status = 1; //系统标定		
	}
	else if(NAV_Data_Full.Nav_Status == E_NAV_STATUS_IN_NAV)
	{
		NAV_Data_Out_temp->Nav_Status = 2;//正常导航
	}
	else if(NAV_Data_Full.Nav_Status == E_NAV_STATUS_STOP)
	{
		NAV_Data_Out_temp->Nav_Status = 128;//导航停止
	}		
}	


void SetNavStatus(unsigned int status)
{
		NAV_Data_Full.pre_Nav_Status	=	NAV_Data_Full.Nav_Status;
		NAV_Data_Full.Nav_Status		=	status;
		g_NavStatusChangeFlag			= 	RETURN_SUCESS;

}
void SetNavStandardFlag(unsigned int flag)
{
	if(NAV_Data_Full.Nav_Standard_flag	!=	flag)
	{
		NAV_Data_Full.pre_Nav_Standard_flag	=	NAV_Data_Full.Nav_Standard_flag;
		NAV_Data_Full.Nav_Standard_flag		=	flag;
		g_NavStandardFlagChangeFlag			=	RETURN_SUCESS;
	}
}

void SetNavFunsionSource(unsigned int flag)
{
	if(NAV_Data_Full.KF.fusion_source 	!=	flag)
	{
#if	0 //重新初始化kalman滤波矩阵效果不好
		if(E_FUNSION_GPS == NAV_Data_Full.KF.fusion_source)
		{
			KF_Init(&NAV_Data_Full);
		}
#endif		
		NAV_Data_Full.KF.pre_fusion_source	=	NAV_Data_Full.KF.fusion_source;
		NAV_Data_Full.KF.fusion_source		=	flag;
		g_NavFunsionSourceChangeFlag		=	RETURN_SUCESS;
	}
	
}

void SetNavRtkStatus(unsigned int flag)
{
	if(flag>=E_GPS_RTK_TOTAL)
	{
#ifdef linux
		inav_log(INAVMD(LOG_DEBUG),"GPS.rtkStatus is error!!! =%d",NAV_Data_Full.GPS.rtkStatus);
#endif
		NAV_Data_Full.GPS.rtkStatus = E_GPS_RTK_INVALID;
	}
	else if(NAV_Data_Full.GPS.rtkStatus 	!=	flag)
	{
		NAV_Data_Full.GPS.pre_rtkStatus	=	NAV_Data_Full.GPS.rtkStatus;
		NAV_Data_Full.GPS.rtkStatus		=	flag;
		g_NavRtkStatusChangeFlag		=	RETURN_SUCESS;
	}
}

#ifdef linux
void PrintOutStateChange(long int navindex,_NAV_Data_Full_t* pNAV_Data_Full)
{
	if(		(RETURN_SUCESS	==	g_NavStandardFlagChangeFlag)
		&&	(pNAV_Data_Full->pre_Nav_Standard_flag != pNAV_Data_Full->Nav_Standard_flag))
	{
		g_NavStandardFlagChangeFlag = RETURN_FAIL;
		inav_log(INAVMD(LOG_DEBUG), "epcho:%10d  %s=>%s"
			,navindex
			,g_NavStandardStaTxt[pNAV_Data_Full->pre_Nav_Standard_flag]
			,g_NavStandardStaTxt[pNAV_Data_Full->Nav_Standard_flag]);
	}
	
	if(		(RETURN_SUCESS	==	g_NavStatusChangeFlag)
		&&	(pNAV_Data_Full->pre_Nav_Status != pNAV_Data_Full->Nav_Status))
	{
		g_NavStatusChangeFlag = RETURN_FAIL;
		inav_log(INAVMD(LOG_DEBUG), "epcho:%10d  %s=>%s"
			,navindex
			,g_NavStatusStaTxt[pNAV_Data_Full->pre_Nav_Status]
			,g_NavStatusStaTxt[pNAV_Data_Full->Nav_Status]);
	}

	if(		(RETURN_SUCESS	==	g_NavFunsionSourceChangeFlag)
		&&	(pNAV_Data_Full->KF.pre_fusion_source != pNAV_Data_Full->KF.fusion_source))
	{
		g_NavFunsionSourceChangeFlag = RETURN_FAIL;
		
		inav_log(INAVMD(LOG_DEBUG), "epcho:%10d  %s=>%s"
			,navindex
			,g_NavFusionSourceStaTxt[pNAV_Data_Full->KF.pre_fusion_source]
			,g_NavFusionSourceStaTxt[pNAV_Data_Full->KF.fusion_source]);
			
	}

	//打印rtk迁移状态
	if( 	(RETURN_SUCESS	==	g_NavRtkStatusChangeFlag)
		&&	(pNAV_Data_Full->GPS.pre_rtkStatus != pNAV_Data_Full->GPS.rtkStatus))
	{
		g_NavRtkStatusChangeFlag = RETURN_FAIL;
		inav_log(INAVMD(LOG_DEBUG), "epcho:%10d  %s=>%s"
			,navindex
			,g_NavRtkStatusStaTxt[pNAV_Data_Full->GPS.pre_rtkStatus]
			,g_NavRtkStatusStaTxt[pNAV_Data_Full->GPS.rtkStatus]);
	}
		
}

#if 0
void PrintALGOStatusInf()
{
	unsigned int len=0;
	memset(g_ALGODebugInfo,0,sizeof(g_ALGODebugInfo));
	sprintf(g_ALGODebugInfo,"$ALGODebug,NavStatus=%d,Standard=%d,imuSelect=%d,memsType=%d,use_gps_flag=%d,fusion=%d\r\n",
		NAV_Data_Full.Nav_Status,
		NAV_Data_Full.Nav_Standard_flag,
		NAV_Data_Full.imuSelect,
		NAV_Data_Full.memsType,
		NAV_Data_Full.KF.use_gps_flag,
		NAV_Data_Full.KF.fusion_source
		);
	len = strlen(g_ALGODebugInfo);	
#ifndef linux
	Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, (unsigned char*)g_ALGODebugInfo);	
#endif
}
#endif

#endif

//////////////////////////////file end/////////////////////////////


