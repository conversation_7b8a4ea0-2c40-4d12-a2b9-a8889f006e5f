/***********************************************************************************
nav status and output module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef _NAV_H_
#define _NAV_H_

#include "nav_type.h"

//#define NAV_USE_FOG                //z��������ݿ��� 

//#define  NAV_DEBUG
 

typedef struct
{   //IMU
	double gyroX;  //deg/s
	double gyroY;
	double gyroZ;
	double accelX; //m/s2
	double accelY;	
	double accelZ;

	double magnetX; //mGauss
	double magnetY;	
	double magnetZ;
	
	//NAV
	double pitch;  //deg
	double roll;
	double heading;	
	double ve;     //m/s
	double vn;
	double vu;
	double latitude;    //deg
	double longitude;   //deg
	double altitude;	//m 
	//gps
	double latitude_gps;
	double longitude_gps;
	double altitude_gps;
	double ve_gps;
	double vn_gps;
	double vu_gps;
	double pitch_gps;
	double roll_gps;
	double heading_gps;
	unsigned char  Sate_Num;	
	unsigned char Position_Status;
	unsigned int rtkStatus;
	unsigned int gpssecond;
	double delay; 
	float  pdop;
	//ods
	  
	//Standard_Data                   //�궨����
	double gyro_off[3];               //������ƫ
	double acc_off[3];                //�ӱ���ƫ
	double gnssAtt_from_vehicle2[3];  //���߰�װ�����
	unsigned char Nav_Standard_flag;        //0:δ�궨 1���궨�� 2���궨���
	
	//sys
	unsigned int Nav_Status;  //0������׼��  1��ϵͳ�궨  2����������   128������ֹͣ

	//���������
	unsigned char imuSelect;
	unsigned char memsType;
	unsigned char use_gps_flag;
	unsigned char fusion_source;
	double lat_std;
	double lon_std;
	double alt_std;
	double pitch_std;
	double roll_std;
	double heading_std;
	double lat_gnss_std;
	double lon_gnss_std;
	double alt_gnss_std;
}_NAV_Data_Out_t;


extern _NAV_Data_Out_t NAV_Data_Out;

//void NAV_function(void);
//void NAV_function_UAV(void);

//void NAV_Oscilloscope2(void);
//void NAV_Oscilloscope3(void);
//void NAV_Oscilloscope4(void);
//void NAV_Oscilloscope5(void);

void Out_Data_Up(_NAV_Data_Out_t* NAV_Data_Out_temp);

void SetNavStatus(unsigned int status);
void SetNavStandardFlag(unsigned int flag);
void SetNavFunsionSource(unsigned int flag);
void SetNavRtkStatus(unsigned int flag);
#ifdef linux 
void PrintOutStateChange(long int navindex,_NAV_Data_Full_t* pNAV_Data_Full);
//void PrintALGOStatusInf();
#endif

void SendOBSIMUData(_NAV_Data_Out_t* NAV_Data_Out_temp,_NAV_Data_Full_t* NAV_Data_Full_p);
void SendOBSGNSSData(_NAV_Data_Out_t* NAV_Data_Out_temp,_NAV_Data_Full_t* NAV_Data_Full_p);
void SendSINSData(_NAV_Data_Out_t* NAV_Data_Out_temp,_NAV_Data_Full_t* NAV_Data_Full_p);



#endif

